import cors from 'cors';
import express from 'express';
import { AccessToken, RoomServiceClient } from 'livekit-server-sdk';

// Initialize LiveKit Room Service Client
const roomService = new RoomServiceClient(
  process.env.LIVEKIT_URL!,
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

// Create token for regular participants
const createToken = async (roomName?: string, participantName?: string) => {
  const room = roomName || 'quickstart-room';
  const participant = participantName || 'quickstart-username';

  const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
    identity: participant,
    // Token to expire after 10 minutes
    ttl: '10m',
  });
  at.addGrant({ roomJoin: true, room });
  return await at.toJwt();
};

// Create token for AI agent
const createAgentToken = async (roomName: string) => {
  const agentIdentity = `ai-agent-${Date.now()}`;

  const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
    identity: agentIdentity,
    // Longer TTL for agents
    ttl: '1h',
  });

  // Grant permissions for agent
  at.addGrant({
    roomJoin: true,
    room: roomName,
    canPublish: true,
    canSubscribe: true,
    canPublishData: true,
  });

  return await at.toJwt();
};

const app = express();
const port = 3000;

app.use(cors());
app.use(express.json());

// Original endpoint for backward compatibility
app.get('/getToken', async (req, res) => {
  try {
    const token = await createToken();
    res.send(token);
  } catch (error) {
    console.error('Error creating token:', error);
    res.status(500).json({ error: 'Failed to create token' });
  }
});

// New endpoint for creating patient rooms with AI agent
app.post('/createPatientRoom', async (req, res) => {
  try {
    const { patientId, patientName } = req.body;

    if (!patientId) {
      return res.status(400).json({ error: 'patientId is required' });
    }

    const roomName = `patient-room-${patientId}`;
    const participantName = patientName || `patient-${patientId}`;

    // Create room if it doesn't exist
    try {
      await roomService.createRoom({
        name: roomName,
        maxParticipants: 2, // Patient + AI Agent
        emptyTimeout: 300, // 5 minutes
        metadata: JSON.stringify({
          type: 'patient-consultation',
          patientId,
          createdAt: new Date().toISOString(),
        }),
      });
      console.log(`Created room: ${roomName}`);
    } catch (error: any) {
      // Room might already exist, which is fine
      if (!error.message?.includes('already exists')) {
        throw error;
      }
    }

    // Create tokens
    const patientToken = await createToken(roomName, participantName);
    const agentToken = await createAgentToken(roomName);

    res.json({
      roomName,
      patientToken,
      agentToken,
      patientIdentity: participantName,
    });

  } catch (error) {
    console.error('Error creating patient room:', error);
    res.status(500).json({ error: 'Failed to create patient room' });
  }
});

// Endpoint to get room information
app.get('/room/:roomName', async (req, res) => {
  try {
    const { roomName } = req.params;
    const room = await roomService.listRooms([roomName]);

    if (room.length === 0) {
      return res.status(404).json({ error: 'Room not found' });
    }

    res.json(room[0]);
  } catch (error) {
    console.error('Error getting room info:', error);
    res.status(500).json({ error: 'Failed to get room information' });
  }
});

// Endpoint to list active rooms
app.get('/rooms', async (req, res) => {
  try {
    const rooms = await roomService.listRooms();
    res.json(rooms);
  } catch (error) {
    console.error('Error listing rooms:', error);
    res.status(500).json({ error: 'Failed to list rooms' });
  }
});

app.listen(port, () => {
  console.log(`Server listening on port ${port}`);
  console.log('Available endpoints:');
  console.log('  GET  /getToken - Get token for default room');
  console.log('  POST /createPatientRoom - Create patient room with AI agent');
  console.log('  GET  /room/:roomName - Get room information');
  console.log('  GET  /rooms - List all active rooms');
});
