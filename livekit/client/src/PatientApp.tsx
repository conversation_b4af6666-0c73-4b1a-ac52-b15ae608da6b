import {
  ControlBar,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
  RoomContext,
  useParticipants,
  useConnectionState,
} from '@livekit/components-react';
import { Room, Track, ConnectionState } from 'livekit-client';
import '@livekit/components-styles';
import { useEffect, useState } from 'react';

const serverUrl = 'http://localhost:3000';
const livekitUrl = 'wss://tryouts-k65g739c.livekit.cloud'; // Default LiveKit server URL

interface PatientRoomResponse {
  roomName: string;
  patientToken: string;
  agentToken: string;
  patientIdentity: string;
}

export default function PatientApp() {
  const [room] = useState(() => new Room({
    adaptiveStream: true,
    dynacast: true,
  }));

  const [roomData, setRoomData] = useState<PatientRoomResponse | null>(null);
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const connectionState = useConnectionState(room);
  const participants = useParticipants({ room });

  // Create patient room and connect
  const createAndJoinRoom = async () => {
    if (!patientId.trim()) {
      setError('Patient ID is required');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      // Create patient room
      const response = await fetch(`${serverUrl}/createPatientRoom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: patientId.trim(),
          patientName: patientName.trim() || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create room: ${response.statusText}`);
      }

      const data: PatientRoomResponse = await response.json();
      setRoomData(data);

      // Connect to the room
      await room.connect(livekitUrl, data.patientToken);

      console.log('Connected to room:', data.roomName);
      console.log('Patient identity:', data.patientIdentity);

    } catch (err) {
      console.error('Error creating/joining room:', err);
      setError(err instanceof Error ? err.message : 'Failed to connect');
    } finally {
      setIsConnecting(false);
    }
  };

  // Disconnect from room
  const disconnectRoom = () => {
    room.disconnect();
    setRoomData(null);
    setError(null);
  };

  // Auto-enable microphone when connected
  useEffect(() => {
    if (connectionState === ConnectionState.Connected) {
      room.localParticipant.enableCameraAndMicrophone().catch(console.error);
    }
  }, [connectionState, room]);

  // Render connection form
  if (!roomData || connectionState === ConnectionState.Disconnected) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        padding: '20px',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{
          background: 'white',
          padding: '40px',
          borderRadius: '10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: '400px',
          width: '100%'
        }}>
          <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>
            AI Patient Consultation
          </h1>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Patient ID *
            </label>
            <input
              type="text"
              value={patientId}
              onChange={(e) => setPatientId(e.target.value)}
              placeholder="Enter patient ID"
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '5px',
                fontSize: '16px'
              }}
            />
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Patient Name (Optional)
            </label>
            <input
              type="text"
              value={patientName}
              onChange={(e) => setPatientName(e.target.value)}
              placeholder="Enter patient name"
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #ddd',
                borderRadius: '5px',
                fontSize: '16px'
              }}
            />
          </div>

          {error && (
            <div style={{
              color: 'red',
              marginBottom: '20px',
              padding: '10px',
              background: '#fee',
              borderRadius: '5px',
              border: '1px solid #fcc'
            }}>
              {error}
            </div>
          )}

          <button
            onClick={createAndJoinRoom}
            disabled={isConnecting || !patientId.trim()}
            style={{
              width: '100%',
              padding: '12px',
              background: isConnecting ? '#ccc' : '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              fontSize: '16px',
              cursor: isConnecting ? 'not-allowed' : 'pointer'
            }}
          >
            {isConnecting ? 'Connecting...' : 'Start Consultation'}
          </button>

          <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
            <p><strong>Instructions:</strong></p>
            <ul style={{ paddingLeft: '20px' }}>
              <li>Enter your patient ID to create a consultation room</li>
              <li>An AI agent will join automatically to assist you</li>
              <li>Make sure your microphone is enabled</li>
              <li>Speak clearly for best results</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  // Render room interface
  return (
    <RoomContext.Provider value={room}>
      <div data-lk-theme="default" style={{ width: "100vw", height: '100vh' }}>
        {/* Header with room info */}
        <div style={{
          background: '#f8f9fa',
          padding: '10px 20px',
          borderBottom: '1px solid #dee2e6',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h3 style={{ margin: 0, color: '#333' }}>
              Patient Consultation - {roomData.patientIdentity}
            </h3>
            <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
              Room: {roomData.roomName} | Participants: {participants.length}
            </p>
          </div>
          <button
            onClick={disconnectRoom}
            style={{
              padding: '8px 16px',
              background: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            End Consultation
          </button>
        </div>

        {/* Video conference area */}
        <PatientVideoConference />

        {/* Audio renderer */}
        <RoomAudioRenderer />

        {/* Control bar */}
        <ControlBar />
      </div>
    </RoomContext.Provider>
  );
}

function PatientVideoConference() {
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  return (
    <GridLayout
      tracks={tracks}
      style={{ height: 'calc(100vh - var(--lk-control-bar-height) - 60px)' }}
    >
      <ParticipantTile />
    </GridLayout>
  );
}
