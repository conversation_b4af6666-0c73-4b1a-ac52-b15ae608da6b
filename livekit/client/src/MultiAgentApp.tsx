import {
  ControlBar,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
  RoomContext,
  useParticipants,
  useConnectionState,
  useDataChannel,
} from '@livekit/components-react';
import { Room, Track, ConnectionState, DataPacket_Kind } from 'livekit-client';
import '@livekit/components-styles';
import { useEffect, useState } from 'react';

const serverUrl = 'http://localhost:3000';
const livekitUrl = 'wss://tryouts-k65g739c.livekit.cloud';

interface PatientRoomResponse {
  roomName: string;
  patientToken: string;
  agentToken: string;
  patientIdentity: string;
}

interface AgentStatus {
  currentAgent: string;
  intent?: string;
  conversationHistory: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    agent?: string;
  }>;
}

export default function MultiAgentApp() {
  const [room] = useState(() => new Room({
    adaptiveStream: true,
    dynacast: true,
  }));

  const [roomData, setRoomData] = useState<PatientRoomResponse | null>(null);
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    currentAgent: 'router',
    conversationHistory: [],
  });

  const connectionState = useConnectionState(room);
  const participants = useParticipants({ room });

  // Listen for data messages from agents
  // useDataChannel((message) => {
  //   try {
  //     const data = JSON.parse(new TextDecoder().decode(message.payload));
  //     if (data.type === 'agent_status') {
  //       setAgentStatus(data.status);
  //     }
  //   } catch (error) {
  //     console.error('Error parsing agent data:', error);
  //   }
  // });

  const createAndJoinRoom = async () => {
    if (!patientId.trim()) {
      setError('Patient ID is required');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      const response = await fetch(`${serverUrl}/createPatientRoom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: patientId.trim(),
          patientName: patientName.trim() || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create room: ${response.statusText}`);
      }

      const data: PatientRoomResponse = await response.json();
      setRoomData(data);
      console.log({data})
      await room.connect(livekitUrl, data.patientToken);

      console.log('Connected to multi-agent room:', data.roomName);

    } catch (err) {
      console.error('Error creating/joining room:', err);
      setError(err instanceof Error ? err.message : 'Failed to connect');
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectRoom = () => {
    room.disconnect();
    setRoomData(null);
    setError(null);
    setAgentStatus({
      currentAgent: 'router',
      conversationHistory: [],
    });
  };

  useEffect(() => {
    if (connectionState === ConnectionState.Connected) {
      room.localParticipant.enableCameraAndMicrophone().catch(console.error);
    }
  }, [connectionState, room]);

  // Render connection form
  if (!roomData || connectionState === ConnectionState.Disconnected) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        padding: '20px',
        fontFamily: 'Arial, sans-serif',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{
          background: 'white',
          padding: '40px',
          borderRadius: '15px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
          maxWidth: '500px',
          width: '100%'
        }}>
          <h1 style={{ textAlign: 'center', marginBottom: '20px', color: '#333' }}>
            🤖 Multi-Agent AI Assistant
          </h1>
          <p style={{ textAlign: 'center', marginBottom: '30px', color: '#666', lineHeight: '1.6' }}>
            Système intelligent avec agents spécialisés pour la prise de rendez-vous,
            l'annulation et les questions générales.
          </p>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Patient ID *
            </label>
            <input
              type="text"
              value={patientId}
              onChange={(e) => setPatientId(e.target.value)}
              placeholder="Entrez votre ID patient"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            />
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Nom du Patient (Optionnel)
            </label>
            <input
              type="text"
              value={patientName}
              onChange={(e) => setPatientName(e.target.value)}
              placeholder="Entrez votre nom"
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            />
          </div>

          {error && (
            <div style={{
              color: '#d32f2f',
              marginBottom: '20px',
              padding: '12px',
              background: '#ffebee',
              borderRadius: '8px',
              border: '1px solid #ffcdd2'
            }}>
              {error}
            </div>
          )}

          <button
            onClick={createAndJoinRoom}
            disabled={isConnecting || !patientId.trim()}
            style={{
              width: '100%',
              padding: '15px',
              background: isConnecting ? '#ccc' : '#4caf50',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: isConnecting ? 'not-allowed' : 'pointer',
              transition: 'background 0.3s'
            }}
          >
            {isConnecting ? 'Connexion...' : '🚀 Démarrer la Consultation'}
          </button>

          <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
            <h3 style={{ marginBottom: '15px', color: '#333' }}>💬 Exemples de conversations :</h3>
            <ul style={{ paddingLeft: '20px', lineHeight: '1.8' }}>
              <li><strong>"Bonjour"</strong> - Salutation</li>
              <li><strong>"Je voudrais prendre rendez-vous"</strong> - Réservation</li>
              <li><strong>"Je dois annuler mon rendez-vous"</strong> - Annulation</li>
              <li><strong>"Quels sont vos horaires ?"</strong> - Questions générales</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  // Render room interface
  return (
    <RoomContext.Provider value={room}>
      <div data-lk-theme="default" style={{ width: "100vw", height: '100vh' }}>
        {/* Header with agent status */}
        <div style={{
          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '15px 20px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h3 style={{ margin: 0 }}>
              🤖 Assistant Multi-Agent - {roomData.patientIdentity}
            </h3>
            <p style={{ margin: '5px 0 0 0', fontSize: '14px', opacity: 0.9 }}>
              Agent actuel: <strong>{getAgentDisplayName(agentStatus.currentAgent)}</strong> |
              Participants: {participants.length} |
              Historique: {agentStatus.conversationHistory.length} messages
            </p>
          </div>
          <button
            onClick={disconnectRoom}
            style={{
              padding: '10px 20px',
              background: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            Terminer
          </button>
        </div>

        {/* Agent Status Panel */}
        <div style={{
          background: '#f8f9fa',
          padding: '10px 20px',
          borderBottom: '1px solid #dee2e6',
          display: 'flex',
          alignItems: 'center',
          gap: '20px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#666' }}>STATUT:</span>
            <span style={{
              padding: '4px 12px',
              background: getAgentColor(agentStatus.currentAgent),
              color: 'white',
              borderRadius: '12px',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>
              {getAgentDisplayName(agentStatus.currentAgent)}
            </span>
          </div>

          {agentStatus.intent && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#666' }}>INTENTION:</span>
              <span style={{
                padding: '4px 12px',
                background: '#17a2b8',
                color: 'white',
                borderRadius: '12px',
                fontSize: '12px',
                fontWeight: 'bold'
              }}>
                {getIntentDisplayName(agentStatus.intent)}
              </span>
            </div>
          )}
        </div>

        {/* Video conference area */}
        <MultiAgentVideoConference />

        {/* Audio renderer */}
        <RoomAudioRenderer />

        {/* Control bar */}
        <ControlBar />
      </div>
    </RoomContext.Provider>
  );
}

function MultiAgentVideoConference() {
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  return (
    <GridLayout
      tracks={tracks}
      style={{ height: 'calc(100vh - var(--lk-control-bar-height) - 120px)' }}
    >
      <ParticipantTile />
    </GridLayout>
  );
}

function getAgentDisplayName(agentName: string): string {
  const names: Record<string, string> = {
    router: 'Routeur',
    booking: 'Réservation',
    cancel: 'Annulation',
    faq: 'Information',
  };
  return names[agentName] || agentName;
}

function getAgentColor(agentName: string): string {
  const colors: Record<string, string> = {
    router: '#6c757d',
    booking: '#28a745',
    cancel: '#dc3545',
    faq: '#007bff',
  };
  return colors[agentName] || '#6c757d';
}

function getIntentDisplayName(intent: string): string {
  const names: Record<string, string> = {
    BOOK: 'Réservation',
    CANCEL: 'Annulation',
    QUESTION: 'Question',
    GREETING: 'Salutation',
    UNKNOWN: 'Inconnu',
  };
  return names[intent] || intent;
}
