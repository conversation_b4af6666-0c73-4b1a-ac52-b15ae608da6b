// Multi-Agent Session Implementation
// Based on the pattern: VoiceAgent -> RouterAgent -> Specialized Agents

import {
  type JobContext,
  WorkerOptions,
  cli,
  defineAgent,
  llm,
  multimodal,
} from '@livekit/agents';
import * as openai from '@livekit/agents-plugin-openai';
import * as deepgram from '@livekit/agents-plugin-deepgram';
import { fileURLToPath } from 'node:url';
import { z } from 'zod';
import { config } from './config.js';
import { logger } from './logger.js';

// Session Memory Interface
interface SessionMemory {
  intent?: 'BOOK' | 'CANCEL' | 'QUESTION' | 'GREETING' | 'UNKNOWN';
  patientName?: string;
  patientId?: string;
  appointment?: {
    date: string;
    time?: string;
    confirmed: boolean;
    type?: string;
  };
  conversationHistory: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    agent?: string;
  }>;
  currentAgent?: string;
  handoffReason?: string;
}

// Agent Context Interface
interface AgentContext {
  session: {
    data: SessionMemory;
    room: any;
    participant: any;
  };
  voiceAgent: any;
  sendMessage: (message: string) => Promise<void>;
  handoffTo: (agentName: string, reason?: string) => Promise<void>;
}

// Base Agent Class
abstract class BaseMultiAgent {
  constructor(
    public name: string,
    public systemPrompt: string,
    public tools: llm.FunctionContext = {}
  ) {}

  abstract async process(input: string, context: AgentContext): Promise<string>;

  protected async callLLM(input: string, context: AgentContext): Promise<string> {
    // Create a simple LLM call - in real implementation, this would use the actual OpenAI model
    const messages = [
      { role: 'system', content: this.systemPrompt },
      ...context.session.data.conversationHistory.slice(-5), // Last 5 messages for context
      { role: 'user', content: input }
    ];

    logger.debug(`${this.name} processing input`, { input, historyLength: context.session.data.conversationHistory.length });

    // Simulate LLM response based on agent type and input
    return await this.generateResponse(input, context);
  }

  protected abstract async generateResponse(input: string, context: AgentContext): Promise<string>;
}

// 1. Router Agent - Detects intent and routes to appropriate agent
class RouterAgent extends BaseMultiAgent {
  constructor() {
    super(
      'RouterAgent',
      `Tu reçois une phrase d'un patient. Tu dois classer la demande dans ce JSON exact : 
      { "intent": "BOOK" | "CANCEL" | "QUESTION" | "GREETING" | "UNKNOWN" }
      
      Exemples:
      - "Je voudrais prendre rendez-vous" -> {"intent": "BOOK"}
      - "J'aimerais annuler mon rendez-vous" -> {"intent": "CANCEL"}
      - "Quels sont vos horaires?" -> {"intent": "QUESTION"}
      - "Bonjour" -> {"intent": "GREETING"}
      - Tout autre cas -> {"intent": "UNKNOWN"}`
    );
  }

  async process(input: string, context: AgentContext): Promise<string> {
    const intent = this.detectIntent(input);
    context.session.data.intent = intent;

    logger.info('Router detected intent', { input, intent });

    return JSON.stringify({ intent });
  }

  protected async generateResponse(input: string, context: AgentContext): Promise<string> {
    const intent = this.detectIntent(input);
    return JSON.stringify({ intent });
  }

  private detectIntent(input: string): SessionMemory['intent'] {
    const lowerInput = input.toLowerCase();

    // Simple keyword-based intent detection
    if (lowerInput.includes('rendez-vous') || lowerInput.includes('rdv') || lowerInput.includes('réserver') || lowerInput.includes('prendre')) {
      return 'BOOK';
    }
    if (lowerInput.includes('annuler') || lowerInput.includes('supprimer') || lowerInput.includes('reporter')) {
      return 'CANCEL';
    }
    if (lowerInput.includes('?') || lowerInput.includes('question') || lowerInput.includes('horaire') || lowerInput.includes('prix')) {
      return 'QUESTION';
    }
    if (lowerInput.includes('bonjour') || lowerInput.includes('salut') || lowerInput.includes('hello')) {
      return 'GREETING';
    }

    return 'UNKNOWN';
  }
}

// 2. Booking Agent - Handles appointment booking
class BookingAgent extends BaseMultiAgent {
  constructor() {
    super(
      'BookingAgent',
      `Tu aides un patient à réserver un rendez-vous médical. Tu es professionnel et bienveillant.
      Tu peux demander des informations comme la date souhaitée, le type de consultation, etc.
      Utilise la fonction createAppointment quand tu as toutes les informations nécessaires.`,
      {
        createAppointment: {
          description: 'Créer un rendez-vous pour le patient',
          parameters: z.object({
            date: z.string().describe('Date du rendez-vous (format: YYYY-MM-DD)'),
            time: z.string().optional().describe('Heure du rendez-vous (format: HH:MM)'),
            type: z.string().optional().describe('Type de consultation'),
          }),
          execute: async ({ date, time, type }) => {
            return `Rendez-vous confirmé pour le ${date}${time ? ` à ${time}` : ''}${type ? ` (${type})` : ''}`;
          },
        },
      }
    );
  }

  async process(input: string, context: AgentContext): Promise<string> {
    return await this.callLLM(input, context);
  }

  protected async generateResponse(input: string, context: AgentContext): Promise<string> {
    // Simple booking logic
    if (input.includes('demain') || input.includes('aujourd\'hui') || /\d{1,2}\/\d{1,2}/.test(input)) {
      const date = this.extractDate(input);
      context.session.data.appointment = {
        date: date || 'à définir',
        confirmed: true
      };
      return `Parfait ! Je vous confirme votre rendez-vous pour le ${date || 'date que vous avez mentionnée'}. Vous recevrez une confirmation par email.`;
    }

    if (!context.session.data.appointment) {
      return "Je serais ravi de vous aider à prendre rendez-vous. Quelle date vous conviendrait le mieux ?";
    }

    return "Y a-t-il autre chose que je puisse faire pour vous concernant votre rendez-vous ?";
  }

  private extractDate(input: string): string | null {
    // Simple date extraction
    if (input.includes('demain')) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString().split('T')[0];
    }
    if (input.includes('aujourd\'hui')) {
      return new Date().toISOString().split('T')[0];
    }

    const dateMatch = input.match(/(\d{1,2})\/(\d{1,2})/);
    if (dateMatch) {
      const [, day, month] = dateMatch;
      const year = new Date().getFullYear();
      return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }

    return null;
  }
}

// 3. Cancel Agent - Handles appointment cancellation
class CancelAgent extends BaseMultiAgent {
  constructor() {
    super(
      'CancelAgent',
      `Tu aides un patient à annuler un rendez-vous. Tu es compréhensif et proposes des alternatives si possible.
      Utilise la fonction cancelAppointment pour confirmer l'annulation.`,
      {
        cancelAppointment: {
          description: 'Annuler un rendez-vous',
          parameters: z.object({
            reason: z.string().describe('Raison de l\'annulation'),
          }),
          execute: async ({ reason }) => {
            return `Le rendez-vous a été annulé. Motif : ${reason}`;
          },
        },
      }
    );
  }

  async process(input: string, context: AgentContext): Promise<string> {
    return await this.callLLM(input, context);
  }

  protected async generateResponse(input: string, context: AgentContext): Promise<string> {
    if (input.toLowerCase().includes('oui') || input.toLowerCase().includes('confirme')) {
      context.session.data.appointment = undefined;
      return "Votre rendez-vous a été annulé avec succès. Souhaitez-vous en reprendre un autre ?";
    }

    return "Je comprends que vous souhaitez annuler votre rendez-vous. Pouvez-vous me confirmer cette annulation ?";
  }
}

// 4. FAQ Agent - Handles general questions
class FaqAgent extends BaseMultiAgent {
  constructor() {
    super(
      'FaqAgent',
      `Tu réponds à des questions générales sur le cabinet médical, les horaires, les tarifs, etc.
      Tu es informatif et rassurant. Si tu ne connais pas la réponse, tu proposes de mettre en contact avec le secrétariat.`
    );
  }

  async process(input: string, context: AgentContext): Promise<string> {
    return await this.callLLM(input, context);
  }

  protected async generateResponse(input: string, context: AgentContext): Promise<string> {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('horaire')) {
      return "Nos horaires d'ouverture sont du lundi au vendredi de 8h à 18h, et le samedi de 9h à 12h. Nous sommes fermés le dimanche.";
    }

    if (lowerInput.includes('prix') || lowerInput.includes('tarif') || lowerInput.includes('coût')) {
      return "Les tarifs varient selon le type de consultation. Une consultation générale coûte 25€. Pour des informations plus précises, je peux vous mettre en contact avec notre secrétariat.";
    }

    if (lowerInput.includes('adresse') || lowerInput.includes('où')) {
      return "Notre cabinet se trouve au 123 Rue de la Santé, 75000 Paris. Nous sommes facilement accessibles en métro (ligne 1, station Santé).";
    }

    return "Je suis là pour répondre à vos questions sur notre cabinet. Que souhaitez-vous savoir exactement ?";
  }
}

// Multi-Agent Session Manager
class MultiAgentSession {
  private memory: SessionMemory;
  private agents: Map<string, BaseMultiAgent>;
  private currentAgent: BaseMultiAgent | null = null;
  private context: AgentContext;
  private voiceSession: any;

  constructor(private jobContext: JobContext, private participant: any) {
    this.memory = {
      conversationHistory: [],
    };

    this.agents = new Map([
      ['router', new RouterAgent()],
      ['booking', new BookingAgent()],
      ['cancel', new CancelAgent()],
      ['faq', new FaqAgent()],
    ]);

    this.context = {
      session: {
        data: this.memory,
        room: jobContext.room,
        participant: this.participant,
      },
      voiceAgent: null, // Will be set later
      sendMessage: this.sendMessage.bind(this),
      handoffTo: this.handoffTo.bind(this),
    };
  }

  async start(): Promise<void> {
    try {
      // Initialize voice capabilities
      await this.initializeVoiceAgent();

      // Send initial greeting
      await this.sendMessage("Bonjour ! Je suis votre assistant virtuel. Comment puis-je vous aider aujourd'hui ?");

      // Start listening for user input
      this.setupEventHandlers();

      logger.info('Multi-agent session started successfully');

    } catch (error) {
      logger.error('Failed to start multi-agent session', { error });
      throw error;
    }
  }

  private async initializeVoiceAgent(): Promise<void> {
    // Configure the AI model
    let model: openai.realtime.RealtimeModel;

    if (config.azure.endpoint) {
      model = openai.realtime.RealtimeModel.withAzure({
        baseURL: config.azure.endpoint,
        azureDeployment: config.azure.deployment!,
        apiKey: config.azure.apiKey,
        entraToken: config.azure.entraToken,
        instructions: 'Tu es un assistant vocal intelligent qui transcrit et traite les demandes des patients.',
        voice: config.agent.voice as any,
        temperature: 0.6, // Lower temperature for more consistent routing
      });
    } else {
      model = new openai.realtime.RealtimeModel({
        apiKey: config.openai.apiKey,
        instructions: 'Tu es un assistant vocal intelligent qui transcrit et traite les demandes des patients.',
        voice: config.agent.voice as any,
        temperature: 0.6,
        model: config.openai.model,
      });
    }

    // Initialize the multimodal agent for voice capabilities
    const agent = new multimodal.MultimodalAgent({
      model,
      fncCtx: {}, // No functions needed for voice transcription
    });


    // Start the voice session
    this.voiceSession = await agent
      .start(this.jobContext.room, this.participant)
      .then((session) => session as openai.realtime.RealtimeSession);

    this.context.voiceAgent = this.voiceSession;
  }

  private setupEventHandlers(): void {
    if (!this.voiceSession) return;

    // Listen for user speech input
    this.voiceSession.on('input_speech_transcription_completed', async (event: any) => {
      const userInput = event.transcript;
      if (userInput && userInput.trim()) {
        await this.processUserInput(userInput);
      }
    });

    this.voiceSession.on('response_done', (response: any) => {
      logger.debug('Voice response completed', { responseId: response.id });
    });

    this.voiceSession.on('error', (error: any) => {
      logger.error('Voice session error', { error: error.message });
    });
  }

  private async processUserInput(input: string): Promise<void> {
    try {
      // Add user input to conversation history
      this.memory.conversationHistory.push({
        role: 'user',
        content: input,
        timestamp: new Date(),
      });

      logger.info('Processing user input', { input, currentAgent: this.memory.currentAgent });

      // If no current agent, start with router
      if (!this.currentAgent) {
        this.currentAgent = this.agents.get('router')!;
        this.memory.currentAgent = 'router';
      }

      // Process input with current agent
      const response = await this.currentAgent.process(input, this.context);

      // Handle router response (intent detection)
      if (this.currentAgent.name === 'RouterAgent') {
        await this.handleRouterResponse(response, input);
      } else {
        // Regular agent response
        await this.sendMessage(response);

        // Add agent response to history
        this.memory.conversationHistory.push({
          role: 'assistant',
          content: response,
          timestamp: new Date(),
          agent: this.currentAgent.name,
        });
      }

    } catch (error) {
      logger.error('Error processing user input', { input, error });
      await this.sendMessage("Désolé, j'ai rencontré un problème. Pouvez-vous répéter votre demande ?");
    }
  }

  private async handleRouterResponse(routerOutput: string, originalInput: string): Promise<void> {
    try {
      const parsed = JSON.parse(routerOutput);
      const intent = parsed.intent;

      logger.info('Router detected intent', { intent, originalInput });

      switch (intent) {
        case 'BOOK':
          await this.handoffTo('booking', 'User wants to book an appointment');
          await this.sendMessage("Je vais vous aider à prendre rendez-vous. Quelle date vous conviendrait ?");
          break;

        case 'CANCEL':
          await this.handoffTo('cancel', 'User wants to cancel an appointment');
          await this.sendMessage("Je comprends que vous souhaitez annuler un rendez-vous. Puis-je vous aider avec cela ?");
          break;

        case 'QUESTION':
          await this.handoffTo('faq', 'User has a general question');
          // Re-process the original input with the FAQ agent
          const faqResponse = await this.currentAgent!.process(originalInput, this.context);
          await this.sendMessage(faqResponse);
          break;

        case 'GREETING':
          await this.sendMessage("Bonjour ! Je suis ravi de vous aider. Que puis-je faire pour vous aujourd'hui ?");
          // Reset to router for next input
          this.currentAgent = this.agents.get('router')!;
          this.memory.currentAgent = 'router';
          break;

        default:
          await this.sendMessage("Je n'ai pas bien compris votre demande. Pouvez-vous me dire si vous souhaitez prendre rendez-vous, annuler un rendez-vous, ou poser une question ?");
          // Stay with router
          break;
      }

    } catch (error) {
      logger.warn('Error parsing router response', { routerOutput, error });
      await this.sendMessage("Je n'ai pas compris. Pouvez-vous reformuler votre demande ?");
    }
  }

  private async sendMessage(message: string): Promise<void> {
    try {
      if (this.voiceSession) {
        // Send message through voice session
        this.voiceSession.conversation.item.create(
          llm.ChatMessage.create({
            role: llm.ChatRole.USER,
            text: `Réponds avec cette phrase exacte: "${message}"`,
          })
        );
        this.voiceSession.response.create();
      }

      logger.info('Sent message to user', { message, agent: this.memory.currentAgent });

    } catch (error) {
      logger.error('Error sending message', { message, error });
    }
  }

  private async handoffTo(agentName: string, reason?: string): Promise<void> {
    const newAgent = this.agents.get(agentName);
    if (!newAgent) {
      logger.error('Unknown agent for handoff', { agentName });
      return;
    }

    const previousAgent = this.memory.currentAgent;
    this.currentAgent = newAgent;
    this.memory.currentAgent = agentName;
    this.memory.handoffReason = reason;

    logger.info('Agent handoff completed', {
      from: previousAgent,
      to: agentName,
      reason
    });
  }
}

// Main agent definition using the multi-agent session
export default defineAgent({
  entry: async (ctx: JobContext) => {
    try {
      await ctx.connect();
      logger.info('Multi-agent system connected to LiveKit room');

      // Wait for a participant to join
      logger.info('Waiting for participant to join...');
      const participant = await ctx.waitForParticipant();
      logger.info(`Starting multi-agent session for participant: ${participant.identity}`);

      // Create and start the multi-agent session
      const session = new MultiAgentSession(ctx, participant);
      await session.start();

      logger.info('Multi-agent session started successfully');

    } catch (error) {
      logger.error('Error in multi-agent entry point', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  },
});

// CLI runner
if (import.meta.url === `file://${process.argv[1]}`) {
  cli.runApp(new WorkerOptions({
    agent: fileURLToPath(import.meta.url),
    logLevel: config.agent.logLevel as any,
  }));
}
