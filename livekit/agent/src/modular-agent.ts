// LiveKit AI Agent with Modular Architecture
// STT (Deepgram) + LLM (OpenAI) + TTS (Cartesia)

import { cli, defineAgent, type JobContext, llm, pipeline, WorkerOptions,  } from '@livekit/agents';
import * as openai from '@livekit/agents-plugin-openai';
import * as deepgram from '@livekit/agents-plugin-deepgram';
import * as cartesia from '@livekit/agents-plugin-cartesia';
import * as silero from '@livekit/agents-plugin-silero';
import { RoomEvent } from 'livekit-client';
import { fileURLToPath } from 'node:url';
import { z } from 'zod';
import { config } from './config.js';
import { logger } from './logger.js';

// Define function context for the AI agent
const createFunctionContext = (): llm.FunctionContext => ({
  getCurrentTime: {
    description: 'Get the current date and time',
    parameters: z.object({}),
    execute: async () => {
      const now = new Date();
      return `Current date and time: ${now.toLocaleString()}`;
    },
  },

  endConversation: {
    description: 'End the current conversation gracefully',
    parameters: z.object({
      reason: z.string().describe('Reason for ending the conversation'),
    }),
    execute: async ({ reason }) => {
      logger.info(`Conversation ended: ${reason}`);
      return `Thank you for our conversation. Take care!`;
    },
  },

  scheduleAppointment: {
    description: 'Schedule a medical appointment',
    parameters: z.object({
      date: z.string().describe('Preferred date for the appointment'),
      time: z.string().optional().describe('Preferred time for the appointment'),
      type: z.string().optional().describe('Type of appointment (consultation, follow-up, etc.)'),
    }),
    execute: async ({ date, time, type }) => {
      logger.info('Scheduling appointment', { date, time, type });
      return `I've scheduled your ${type || 'appointment'} for ${date}${time ? ` at ${time}` : ''}. You'll receive a confirmation shortly.`;
    },
  },
});

// Main agent definition with modular architecture
export default defineAgent({
  entry: async (ctx: JobContext) => {
    console.log({ ctx })
    try {
      await ctx.connect();
      logger.info('Modular agent connected to LiveKit room');

      // Wait for a participant to join
      logger.info('Waiting for participant to join...');
      const participant = await ctx.waitForParticipant();
      logger.info(`Starting modular agent for participant: ${participant.identity}`);

      // 1. Configure Speech-to-Text (Deepgram)
      logger.info('Initializing Deepgram STT...');
      const sttModel = new deepgram.STT({
        apiKey: process.env.DEEPGRAM_API_KEY || config.deepgram?.apiKey,
        model: 'nova-2-general',
        language: 'fr', // French language
        smartFormat: true,
        punctuate: true,
        diarize: false,
      });

      // 2. Configure Voice Activity Detection (Silero VAD)
      logger.info('Initializing Silero VAD...');
      const vadModel = await silero.VAD.load();

      // 3. Configure Language Model (OpenAI)
      logger.info('Initializing OpenAI LLM...');
      let llmModel: openai.LLM;

      // if (config.azure.endpoint) {
      //   logger.info('Using Azure OpenAI for LLM');
      //   llmModel = new openai.LLM.withAzure({
      //     baseURL: config.azure.endpoint,
      //     azureDeployment: config.azure.deployment!,
      //     apiKey: config.azure.apiKey,
      //     entraToken: config.azure.entraToken,
      //     model: 'gpt-4o',
      //     temperature: config.agent.temperature,
      //   });
      // } else {
      logger.info('Using OpenAI for LLM');
      llmModel = new openai.LLM({
        apiKey: config.openai.apiKey,
        model: 'gpt-4o',
        temperature: config.agent.temperature,
      });
      // }

      // 4. Configure Text-to-Speech (Cartesia)
      logger.info('Initializing Cartesia TTS...');
      const ttsModel = new cartesia.TTS({
        apiKey: process.env.CARTESIA_API_KEY || config.cartesia?.apiKey,
        voice: config.agent.voice || 'a0e99841-438c-4a64-b679-ae501e7d6091', // Cartesia voice ID
        language: 'fr', // French language
        speed: 1.0,
        emotion: ['positivity:high', 'curiosity:high'],
      });

      // 5. Create function context
      const fncCtx = createFunctionContext();

      // 6. Create Voice Pipeline Agent
      logger.info('Creating voice pipeline agent...');
      const agent = new pipeline.VoicePipelineAgent(
        vadModel,
        new openai.STT({ language: "fr" }),
        llmModel,
        new openai.TTS(),
        {
          fncCtx,
          chatCtx: new llm.ChatContext({
            messages: [
              {
                role: llm.ChatRole.SYSTEM,
                content: buildSystemPrompt(),
              },
            ],
          }),
          // Pipeline configuration
          allowInterruptions: true,
          // Lower these values to make the agent more responsive
          interruptSpeechDuration: 300,
          interruptMinWords: 0,
          // Reduce silence detection threshold
          minEndpointingDelay: 300,
          preemptiveSynthesis: true,
      });

      // Set up event listeners
      agent.on(pipeline.VPAEvent.USER_STARTED_SPEAKING, () => {
        logger.info('User started speaking');
      });

      agent.on(pipeline.VPAEvent.USER_STOPPED_SPEAKING, () => {
        logger.info('User stopped speaking');
      });

      agent.on(pipeline.VPAEvent.USER_SPEECH_COMMITTED, (msg: llm.ChatMessage) => {
        logger.info('User speech committed', {
          text: msg.content,
          msg,
        });
      });

      agent.on('agent_speech_start', () => {
        logger.info('Agent started speaking');
      });

      agent.on('agent_speech_end', () => {
        logger.info('Agent stopped speaking');
      });

      agent.on('agent_speech_committed', (event) => {
        logger.info('Agent speech committed', {
          text: event.text,
          duration: event.duration
        });
      });

      agent.on('error', (error) => {
        logger.error('Voice pipeline error occurred', { error: error.message });
      });

      logger.info('Starting voice pipeline agent session...');

      // Start the agent
      await agent.start(ctx.room, participant);
      // Send initial greeting
      logger.info('Sending initial greeting to participant');
      await agent.say("Bonjour ! Je suis votre assistant médical virtuel. Comment puis-je vous aider aujourd'hui ?");

      logger.info('Modular agent session started successfully');

    } catch (error) {
      logger.error('Error in modular agent entry point', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  },
});

// Build system prompt for the LLM
function buildSystemPrompt(): string {
  return `Tu es un assistant médical virtuel intelligent et bienveillant. Voici tes directives :

RÔLE ET PERSONNALITÉ :
- Tu es professionnel, empathique et rassurant
- Tu parles français de manière naturelle et conversationnelle
- Tu es patient et à l'écoute des préoccupations des patients
- Tu maintiens un ton chaleureux mais professionnel

CAPACITÉS :
- Répondre aux questions générales sur la santé
- Aider à prendre des rendez-vous médicaux
- Fournir des informations sur le cabinet médical
- Orienter vers les professionnels appropriés si nécessaire

LIMITATIONS IMPORTANTES :
- Tu ne poses JAMAIS de diagnostic médical
- Tu ne prescris JAMAIS de médicaments
- Tu recommandes toujours de consulter un professionnel de santé pour les problèmes médicaux
- Tu ne remplaces pas une consultation médicale

FONCTIONS DISPONIBLES :
- getCurrentTime() : obtenir l'heure actuelle
- scheduleAppointment() : programmer un rendez-vous
- endConversation() : terminer la conversation

INSTRUCTIONS DE CONVERSATION :
- Écoute attentivement les demandes du patient
- Pose des questions de clarification si nécessaire
- Utilise les fonctions disponibles quand c'est approprié
- Reste dans ton domaine de compétence
- Redirige vers un professionnel si la demande dépasse tes capacités

INFORMATIONS DU CABINET :
- Horaires : Lundi-Vendredi 8h-18h, Samedi 9h-12h
- Adresse : 123 Rue de la Santé, 75000 Paris
- Téléphone : 01 23 45 67 89
- Email : <EMAIL>

Réponds de manière naturelle et conversationnelle, comme si tu parlais vraiment avec le patient.
PARLE EN FRANCAIS`;
}

// CLI runner
if (import.meta.url === `file://${process.argv[1]}`) {
  cli.runApp(new WorkerOptions({
    agent: fileURLToPath(import.meta.url),
    logLevel: config.agent.logLevel as any,
  }));
}
