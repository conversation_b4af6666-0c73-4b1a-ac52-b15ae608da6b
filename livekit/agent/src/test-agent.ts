// Test script to verify agent configuration and basic functionality
import { config, validateConfig } from './config.js';
import { logger } from './logger.js';

async function testConfiguration() {
  logger.info('🧪 Testing LiveKit AI Agent Configuration');
  
  try {
    // Test configuration validation
    logger.info('✅ Configuration validation passed');
    
    // Test LiveKit connection parameters
    logger.info('LiveKit Configuration:', {
      url: config.livekit.url,
      hasApiKey: !!config.livekit.apiKey,
      hasApiSecret: !!config.livekit.apiSecret,
    });
    
    // Test AI provider configuration
    if (config.azure.endpoint) {
      logger.info('Using Azure OpenAI:', {
        endpoint: config.azure.endpoint,
        deployment: config.azure.deployment,
        hasApiKey: !!config.azure.apiKey,
        hasEntraToken: !!config.azure.entraToken,
      });
    } else {
      logger.info('Using OpenAI:', {
        hasApiKey: !!config.openai.apiKey,
        model: config.openai.model,
      });
    }
    
    // Test agent configuration
    logger.info('Agent Configuration:', {
      voice: config.agent.voice,
      temperature: config.agent.temperature,
      logLevel: config.agent.logLevel,
      maxDuration: config.agent.maxConversationDuration,
    });
    
    // Test audio configuration
    logger.info('Audio Configuration:', {
      sampleRate: config.audio.sampleRate,
      channels: config.audio.channels,
    });
    
    logger.info('🎉 All configuration tests passed!');
    logger.info('💡 You can now run the agent with: bun run dev');
    
  } catch (error) {
    logger.error('❌ Configuration test failed:', {
      error: error instanceof Error ? error.message : String(error),
    });
    process.exit(1);
  }
}

// Test OpenAI connection (optional)
async function testOpenAIConnection() {
  if (!config.openai.apiKey && !config.azure.apiKey) {
    logger.warn('⚠️  No OpenAI API key found, skipping connection test');
    return;
  }
  
  try {
    logger.info('🔌 Testing OpenAI connection...');
    
    // Simple test to verify API key works
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${config.openai.apiKey}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (response.ok) {
      logger.info('✅ OpenAI connection successful');
    } else {
      logger.warn('⚠️  OpenAI connection failed:', {
        status: response.status,
        statusText: response.statusText,
      });
    }
  } catch (error) {
    logger.warn('⚠️  OpenAI connection test failed:', {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

// Main test function
async function runTests() {
  console.log('🚀 LiveKit AI Agent Test Suite\n');
  
  await testConfiguration();
  await testOpenAIConnection();
  
  console.log('\n📋 Next Steps:');
  console.log('1. Start your LiveKit server');
  console.log('2. Start the enhanced server: cd ../server && bun run server.ts');
  console.log('3. Start the agent: bun run dev');
  console.log('4. Create a patient room via POST /createPatientRoom');
  console.log('5. Connect your client to test the conversation');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch((error) => {
    logger.error('Test suite failed:', error);
    process.exit(1);
  });
}
