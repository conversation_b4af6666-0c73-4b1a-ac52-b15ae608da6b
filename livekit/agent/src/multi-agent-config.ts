// Multi-Agent System Configuration

export interface MultiAgentConfig {
  // Router Agent Configuration
  router: {
    enabled: boolean;
    temperature: number;
    maxRetries: number;
    fallbackIntent: string;
  };

  // Booking Agent Configuration
  booking: {
    enabled: boolean;
    availableTimeSlots: string[];
    maxAdvanceBookingDays: number;
    confirmationRequired: boolean;
    allowedAppointmentTypes: string[];
  };

  // Cancel Agent Configuration
  cancel: {
    enabled: boolean;
    requireReason: boolean;
    allowRescheduling: boolean;
    cancellationDeadlineHours: number;
  };

  // FAQ Agent Configuration
  faq: {
    enabled: boolean;
    knowledgeBase: {
      hours: string;
      address: string;
      phone: string;
      email: string;
      pricing: Record<string, string>;
      services: string[];
    };
    escalationKeywords: string[];
  };

  // Session Configuration
  session: {
    maxConversationLength: number;
    inactivityTimeoutMinutes: number;
    enableConversationHistory: boolean;
    enableHandoffLogging: boolean;
  };

  // Voice Configuration
  voice: {
    language: string;
    voice: string;
    speed: number;
    enableTranscription: boolean;
    enableSpeechToText: boolean;
  };
}

export const defaultMultiAgentConfig: MultiAgentConfig = {
  router: {
    enabled: true,
    temperature: 0, // Low temperature for consistent intent detection
    maxRetries: 3,
    fallbackIntent: 'UNKNOWN',
  },

  booking: {
    enabled: true,
    availableTimeSlots: [
      '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
      '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'
    ],
    maxAdvanceBookingDays: 30,
    confirmationRequired: true,
    allowedAppointmentTypes: [
      'Consultation générale',
      'Consultation spécialisée',
      'Suivi',
      'Urgence'
    ],
  },

  cancel: {
    enabled: true,
    requireReason: false,
    allowRescheduling: true,
    cancellationDeadlineHours: 24,
  },

  faq: {
    enabled: true,
    knowledgeBase: {
      hours: 'Lundi-Vendredi: 8h-18h, Samedi: 9h-12h, Fermé le dimanche',
      address: '123 Rue de la Santé, 75000 Paris',
      phone: '01 23 45 67 89',
      email: '<EMAIL>',
      pricing: {
        'consultation_generale': '25€',
        'consultation_specialisee': '50€',
        'suivi': '20€',
        'urgence': '35€',
      },
      services: [
        'Médecine générale',
        'Cardiologie',
        'Dermatologie',
        'Pédiatrie',
        'Vaccinations',
        'Bilans de santé'
      ],
    },
    escalationKeywords: [
      'urgence',
      'douleur',
      'grave',
      'médecin',
      'docteur',
      'secrétariat'
    ],
  },

  session: {
    maxConversationLength: 50, // Maximum number of exchanges
    inactivityTimeoutMinutes: 10,
    enableConversationHistory: true,
    enableHandoffLogging: true,
  },

  voice: {
    language: 'fr-FR',
    voice: 'alloy',
    speed: 1.0,
    enableTranscription: true,
    enableSpeechToText: true,
  },
};

// Environment-based configuration override
export function getMultiAgentConfig(): MultiAgentConfig {
  const config = { ...defaultMultiAgentConfig };

  // Override with environment variables if available
  if (process.env.MULTI_AGENT_ROUTER_ENABLED !== undefined) {
    config.router.enabled = process.env.MULTI_AGENT_ROUTER_ENABLED === 'true';
  }

  if (process.env.MULTI_AGENT_BOOKING_ENABLED !== undefined) {
    config.booking.enabled = process.env.MULTI_AGENT_BOOKING_ENABLED === 'true';
  }

  if (process.env.MULTI_AGENT_CANCEL_ENABLED !== undefined) {
    config.cancel.enabled = process.env.MULTI_AGENT_CANCEL_ENABLED === 'true';
  }

  if (process.env.MULTI_AGENT_FAQ_ENABLED !== undefined) {
    config.faq.enabled = process.env.MULTI_AGENT_FAQ_ENABLED === 'true';
  }

  if (process.env.MULTI_AGENT_VOICE_LANGUAGE) {
    config.voice.language = process.env.MULTI_AGENT_VOICE_LANGUAGE;
  }

  if (process.env.MULTI_AGENT_MAX_CONVERSATION_LENGTH) {
    config.session.maxConversationLength = parseInt(process.env.MULTI_AGENT_MAX_CONVERSATION_LENGTH);
  }

  if (process.env.MULTI_AGENT_INACTIVITY_TIMEOUT) {
    config.session.inactivityTimeoutMinutes = parseInt(process.env.MULTI_AGENT_INACTIVITY_TIMEOUT);
  }

  // Override knowledge base from environment
  if (process.env.CABINET_HOURS) {
    config.faq.knowledgeBase.hours = process.env.CABINET_HOURS;
  }

  if (process.env.CABINET_ADDRESS) {
    config.faq.knowledgeBase.address = process.env.CABINET_ADDRESS;
  }

  if (process.env.CABINET_PHONE) {
    config.faq.knowledgeBase.phone = process.env.CABINET_PHONE;
  }

  if (process.env.CABINET_EMAIL) {
    config.faq.knowledgeBase.email = process.env.CABINET_EMAIL;
  }

  return config;
}

// Validation function
export function validateMultiAgentConfig(config: MultiAgentConfig): string[] {
  const errors: string[] = [];

  // Validate that at least one agent is enabled
  const enabledAgents = [
    config.router.enabled,
    config.booking.enabled,
    config.cancel.enabled,
    config.faq.enabled,
  ].filter(Boolean);

  if (enabledAgents.length === 0) {
    errors.push('At least one agent must be enabled');
  }

  // Router must be enabled if other agents are enabled
  if (!config.router.enabled && enabledAgents.length > 0) {
    errors.push('Router agent must be enabled when other agents are enabled');
  }

  // Validate time slots format
  for (const slot of config.booking.availableTimeSlots) {
    if (!/^\d{2}:\d{2}$/.test(slot)) {
      errors.push(`Invalid time slot format: ${slot}. Expected format: HH:MM`);
    }
  }

  // Validate numeric values
  if (config.booking.maxAdvanceBookingDays <= 0) {
    errors.push('maxAdvanceBookingDays must be greater than 0');
  }

  if (config.cancel.cancellationDeadlineHours < 0) {
    errors.push('cancellationDeadlineHours must be non-negative');
  }

  if (config.session.maxConversationLength <= 0) {
    errors.push('maxConversationLength must be greater than 0');
  }

  if (config.session.inactivityTimeoutMinutes <= 0) {
    errors.push('inactivityTimeoutMinutes must be greater than 0');
  }

  if (config.voice.speed <= 0 || config.voice.speed > 2) {
    errors.push('voice speed must be between 0 and 2');
  }

  return errors;
}

// Helper function to get agent-specific configuration
export function getAgentConfig(agentName: string, config: MultiAgentConfig): any {
  switch (agentName) {
    case 'router':
      return config.router;
    case 'booking':
      return config.booking;
    case 'cancel':
      return config.cancel;
    case 'faq':
      return config.faq;
    default:
      return null;
  }
}

// Export the configuration instance
export const multiAgentConfig = getMultiAgentConfig();
