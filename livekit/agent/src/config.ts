// Configuration management for LiveKit AI Agent
import 'dotenv/config';

export interface AgentConfig {
  // LiveKit Configuration
  livekit: {
    url: string;
    apiKey: string;
    apiSecret: string;
  };

  // OpenAI Configuration
  openai: {
    apiKey: string;
    model: string;
  };

  // Azure OpenAI Configuration (optional)
  azure: {
    endpoint?: string;
    deployment?: string;
    apiKey?: string;
    entraToken?: string;
  };

  // Deepgram Configuration
  deepgram: {
    apiKey: string;
    model: string;
    language: string;
  };

  // Cartesia Configuration
  cartesia: {
    apiKey: string;
    voice: string;
    language: string;
  };

  // Agent Behavior Configuration
  agent: {
    systemPrompt: string;
    initialGreeting: string;
    voice: string;
    temperature: number;
    logLevel: string;
    maxConversationDuration: number; // in minutes
  };

  // Audio Configuration
  audio: {
    sampleRate: number;
    channels: number;
  };
}

// Validate required environment variables
const validateEnvVar = (name: string, value: string | undefined): string => {
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
};

// Default system prompt for the AI agent
const DEFAULT_SYSTEM_PROMPT = `You are a helpful AI assistant designed to have natural conversations with patients or users. 

Key guidelines:
- Be empathetic, professional, and supportive
- Keep responses concise but informative
- Ask clarifying questions when needed
- If discussing health topics, remind users to consult healthcare professionals
- Maintain patient confidentiality and privacy
- End conversations gracefully when appropriate
- Speak naturally as if having a real conversation

You can access the current time and end conversations when appropriate using the available functions.`;

// Configuration object
export const config: AgentConfig = {
  livekit: {
    url: validateEnvVar('LIVEKIT_URL', process.env.LIVEKIT_URL),
    apiKey: validateEnvVar('LIVEKIT_API_KEY', process.env.LIVEKIT_API_KEY),
    apiSecret: validateEnvVar('LIVEKIT_API_SECRET', process.env.LIVEKIT_API_SECRET),
  },

  openai: {
    apiKey: validateEnvVar('OPENAI_API_KEY', process.env.OPENAI_API_KEY),
    model: process.env.OPENAI_MODEL || 'gpt-4o-realtime-preview-2024-10-01',
  },

  azure: {
    endpoint: process.env.AZURE_OPENAI_ENDPOINT,
    deployment: process.env.AZURE_OPENAI_DEPLOYMENT,
    apiKey: process.env.AZURE_OPENAI_API_KEY,
    entraToken: process.env.AZURE_OPENAI_ENTRA_TOKEN,
  },

  deepgram: {
    apiKey: process.env.DEEPGRAM_API_KEY || 'test-key',
    model: process.env.DEEPGRAM_MODEL || 'nova-2',
    language: process.env.DEEPGRAM_LANGUAGE || 'fr',
  },

  cartesia: {
    apiKey: process.env.CARTESIA_API_KEY || 'test-key',
    voice: process.env.CARTESIA_VOICE || 'a0e99841-438c-4a64-b679-ae501e7d6091',
    language: process.env.CARTESIA_LANGUAGE || 'fr',
  },

  agent: {
    systemPrompt: process.env.AGENT_SYSTEM_PROMPT || DEFAULT_SYSTEM_PROMPT,
    initialGreeting: process.env.AGENT_INITIAL_GREETING || 'Hello! I\'m your AI assistant. How can I help you today?',
    voice: process.env.AGENT_VOICE || 'alloy',
    temperature: parseFloat(process.env.AGENT_TEMPERATURE || '0.7'),
    logLevel: process.env.AGENT_LOG_LEVEL || 'info',
    maxConversationDuration: parseInt(process.env.AGENT_MAX_DURATION || '30'),
  },

  audio: {
    sampleRate: parseInt(process.env.AUDIO_SAMPLE_RATE || '24000'),
    channels: parseInt(process.env.AUDIO_CHANNELS || '1'),
  },
};

// Validate configuration
export const validateConfig = (): void => {
  // Check if either OpenAI or Azure configuration is complete
  const hasOpenAI = config.openai.apiKey;
  const hasAzure = config.azure.endpoint && config.azure.deployment &&
                   (config.azure.apiKey || config.azure.entraToken);

  if (!hasOpenAI && !hasAzure) {
    throw new Error(
      'Either OpenAI API key or complete Azure OpenAI configuration is required'
    );
  }

  // Validate Deepgram configuration (only in production)
  if (process.env.NODE_ENV === 'production' && (!config.deepgram.apiKey || config.deepgram.apiKey === 'test-key')) {
    throw new Error('Deepgram API key is required for speech-to-text in production');
  }

  // Validate Cartesia configuration (only in production)
  if (process.env.NODE_ENV === 'production' && (!config.cartesia.apiKey || config.cartesia.apiKey === 'test-key')) {
    throw new Error('Cartesia API key is required for text-to-speech in production');
  }

  // Validate voice option
  const validVoices = ['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'];
  if (!validVoices.includes(config.agent.voice)) {
    throw new Error(
      `Invalid voice option: ${config.agent.voice}. Valid options: ${validVoices.join(', ')}`
    );
  }

  // Validate temperature
  if (config.agent.temperature < 0 || config.agent.temperature > 2) {
    throw new Error('Temperature must be between 0 and 2');
  }

  console.log('✅ Configuration validated successfully');
};

// Initialize and validate configuration
validateConfig();
