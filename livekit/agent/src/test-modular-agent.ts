// Test script for Modular Agent (STT + LLM + TTS)

import { config } from './config.js';

// Simple logger for testing
const testLogger = {
  info: (message: string, meta?: any) => {
    console.log(`[INFO] ${message}`, meta ? JSON.stringify(meta, null, 2) : '');
  },
  error: (message: string, meta?: any) => {
    console.error(`[ERROR] ${message}`, meta ? JSON.stringify(meta, null, 2) : '');
  },
  warn: (message: string, meta?: any) => {
    console.warn(`[WARN] ${message}`, meta ? JSON.stringify(meta, null, 2) : '');
  },
  debug: (message: string, meta?: any) => {
    console.log(`[DEBUG] ${message}`, meta ? JSON.stringify(meta, null, 2) : '');
  },
};

async function testModularConfiguration(): Promise<void> {
  testLogger.info('🧪 Testing Modular Agent Configuration');
  
  try {
    // Test LiveKit configuration
    testLogger.info('LiveKit Configuration:', {
      url: config.livekit.url,
      hasApiKey: !!config.livekit.apiKey,
      hasApiSecret: !!config.livekit.apiSecret,
    });
    
    // Test LLM configuration
    if (config.azure.endpoint) {
      testLogger.info('LLM: Using Azure OpenAI:', {
        endpoint: config.azure.endpoint,
        deployment: config.azure.deployment,
        hasApiKey: !!config.azure.apiKey,
        hasEntraToken: !!config.azure.entraToken,
      });
    } else {
      testLogger.info('LLM: Using OpenAI:', {
        hasApiKey: !!config.openai.apiKey,
        model: config.openai.model,
      });
    }
    
    // Test STT configuration (Deepgram)
    testLogger.info('STT: Deepgram Configuration:', {
      hasApiKey: !!config.deepgram.apiKey,
      model: config.deepgram.model,
      language: config.deepgram.language,
    });
    
    // Test TTS configuration (Cartesia)
    testLogger.info('TTS: Cartesia Configuration:', {
      hasApiKey: !!config.cartesia.apiKey,
      voice: config.cartesia.voice,
      language: config.cartesia.language,
    });
    
    // Test agent configuration
    testLogger.info('Agent Configuration:', {
      voice: config.agent.voice,
      temperature: config.agent.temperature,
      logLevel: config.agent.logLevel,
      maxDuration: config.agent.maxConversationDuration,
    });
    
    testLogger.info('✅ All modular agent configuration tests passed!');
    
  } catch (error) {
    testLogger.error('❌ Configuration test failed:', {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

async function testAPIConnections(): Promise<void> {
  testLogger.info('🔌 Testing API Connections');
  
  // Test OpenAI connection
  if (config.openai.apiKey && !config.azure.endpoint) {
    try {
      testLogger.info('Testing OpenAI connection...');
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${config.openai.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        testLogger.info('✅ OpenAI connection successful');
      } else {
        testLogger.warn('⚠️  OpenAI connection failed:', {
          status: response.status,
          statusText: response.statusText,
        });
      }
    } catch (error) {
      testLogger.warn('⚠️  OpenAI connection test failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
  
  // Test Deepgram connection
  if (config.deepgram.apiKey) {
    try {
      testLogger.info('Testing Deepgram connection...');
      const response = await fetch('https://api.deepgram.com/v1/projects', {
        headers: {
          'Authorization': `Token ${config.deepgram.apiKey}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        testLogger.info('✅ Deepgram connection successful');
      } else {
        testLogger.warn('⚠️  Deepgram connection failed:', {
          status: response.status,
          statusText: response.statusText,
        });
      }
    } catch (error) {
      testLogger.warn('⚠️  Deepgram connection test failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  } else {
    testLogger.warn('⚠️  No Deepgram API key found, skipping connection test');
  }
  
  // Test Cartesia connection
  if (config.cartesia.apiKey) {
    try {
      testLogger.info('Testing Cartesia connection...');
      const response = await fetch('https://api.cartesia.ai/voices', {
        headers: {
          'X-API-Key': config.cartesia.apiKey,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        testLogger.info('✅ Cartesia connection successful');
      } else {
        testLogger.warn('⚠️  Cartesia connection failed:', {
          status: response.status,
          statusText: response.statusText,
        });
      }
    } catch (error) {
      testLogger.warn('⚠️  Cartesia connection test failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  } else {
    testLogger.warn('⚠️  No Cartesia API key found, skipping connection test');
  }
}

async function testModularArchitecture(): Promise<void> {
  testLogger.info('🏗️  Testing Modular Architecture');
  
  const components = [
    {
      name: 'Speech-to-Text (STT)',
      provider: 'Deepgram',
      model: config.deepgram.model,
      language: config.deepgram.language,
      configured: !!config.deepgram.apiKey,
    },
    {
      name: 'Large Language Model (LLM)',
      provider: config.azure.endpoint ? 'Azure OpenAI' : 'OpenAI',
      model: config.azure.endpoint ? config.azure.deployment : config.openai.model,
      configured: !!(config.openai.apiKey || (config.azure.endpoint && config.azure.apiKey)),
    },
    {
      name: 'Text-to-Speech (TTS)',
      provider: 'Cartesia',
      voice: config.cartesia.voice,
      language: config.cartesia.language,
      configured: !!config.cartesia.apiKey,
    },
    {
      name: 'Voice Activity Detection (VAD)',
      provider: 'Silero',
      model: 'silero_vad',
      configured: true, // Built-in, no API key needed
    },
  ];
  
  testLogger.info('📋 Modular Architecture Components:');
  
  let allConfigured = true;
  for (const component of components) {
    if (component.configured) {
      testLogger.info(`✅ ${component.name}:`, {
        provider: component.provider,
        model: component.model || 'default',
        language: component.language || 'N/A',
      });
    } else {
      testLogger.error(`❌ ${component.name}:`, {
        provider: component.provider,
        status: 'Not configured - missing API key',
      });
      allConfigured = false;
    }
  }
  
  if (allConfigured) {
    testLogger.info('✅ All modular components are properly configured!');
  } else {
    testLogger.warn('⚠️  Some components are missing configuration');
  }
}

async function testVoicePipelineFeatures(): Promise<void> {
  testLogger.info('🎤 Testing Voice Pipeline Features');
  
  const features = [
    {
      name: 'Interruption Support',
      description: 'Allow users to interrupt agent speech',
      enabled: true,
      config: 'allowInterruptions: true',
    },
    {
      name: 'Preemptive Synthesis',
      description: 'Start generating speech before LLM completes',
      enabled: true,
      config: 'preemptiveSynthesis: true',
    },
    {
      name: 'Smart Endpointing',
      description: 'Detect when user has finished speaking',
      enabled: true,
      config: 'minEndpointingDelay: 0.5s',
    },
    {
      name: 'Function Calling',
      description: 'Execute functions based on conversation',
      enabled: true,
      functions: ['getCurrentTime', 'scheduleAppointment', 'endConversation'],
    },
    {
      name: 'Multi-language Support',
      description: 'Support for French language',
      enabled: true,
      language: 'French (fr)',
    },
  ];
  
  testLogger.info('🚀 Voice Pipeline Features:');
  
  for (const feature of features) {
    testLogger.info(`✅ ${feature.name}:`, {
      description: feature.description,
      enabled: feature.enabled,
      config: feature.config || 'default',
      functions: feature.functions || undefined,
      language: feature.language || undefined,
    });
  }
}

async function runModularAgentTests(): Promise<void> {
  console.log('🚀 Modular Agent Test Suite (STT + LLM + TTS)\n');
  
  try {
    await testModularConfiguration();
    console.log('');
    
    await testAPIConnections();
    console.log('');
    
    await testModularArchitecture();
    console.log('');
    
    await testVoicePipelineFeatures();
    console.log('');
    
    testLogger.info('🎉 All modular agent tests completed!');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Ensure you have API keys for:');
    console.log('   - Deepgram (STT): DEEPGRAM_API_KEY');
    console.log('   - OpenAI (LLM): OPENAI_API_KEY');
    console.log('   - Cartesia (TTS): CARTESIA_API_KEY');
    console.log('2. Start your LiveKit server');
    console.log('3. Start the enhanced server: cd ../server && bun run server.ts');
    console.log('4. Start the modular agent: bun run dev:modular');
    console.log('5. Create a patient room via POST /createPatientRoom');
    console.log('6. Connect your client and test voice conversations');
    
    console.log('\n🎯 Architecture Benefits:');
    console.log('   ✅ Modular: Each component can be swapped independently');
    console.log('   ✅ Optimized: Best-in-class providers for each function');
    console.log('   ✅ Flexible: Easy to configure and customize');
    console.log('   ✅ Scalable: Components can be scaled separately');
    console.log('   ✅ Reliable: Dedicated providers for STT, LLM, and TTS');
    
  } catch (error) {
    testLogger.error('❌ Modular agent test suite failed:', { error });
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runModularAgentTests().catch((error) => {
    testLogger.error('Test suite failed:', error);
    process.exit(1);
  });
}
