// LiveKit AI Agent for Real-time Conversations
// Supports 1:1 patient-bot conversations with speech-to-text, AI responses, and text-to-speech

import {
  type JobContext,
  WorkerOptions,
  cli,
  defineAgent,
  llm,
  multimodal
} from '@livekit/agents';
import * as openai from '@livekit/agents-plugin-openai';
import * as deepgram from '@livekit/agents-plugin-deepgram';
import * as silero from '@livekit/agents-plugin-silero';
import { fileURLToPath } from 'node:url';
import { z } from 'zod';
import { config } from './config.js';
import { logger } from './logger.js';

// Define function context for the AI agent
const createFunctionContext = (): llm.FunctionContext => ({
  getCurrentTime: {
    description: 'Get the current date and time',
    parameters: z.object({}),
    execute: async () => {
      const now = new Date();
      return `Current date and time: ${now.toLocaleString()}`;
    },
  },

  endConversation: {
    description: 'End the current conversation gracefully',
    parameters: z.object({
      reason: z.string().describe('Reason for ending the conversation'),
    }),
    execute: async ({ reason }) => {
      logger.info(`Conversation ended: ${reason}`);
      return `Thank you for our conversation. Take care!`;
    },
  },
});

// Main agent definition
export default defineAgent({
  entry: async (ctx: JobContext) => {
    try {
      await ctx.connect();
      logger.info('Agent connected to LiveKit room');

      // Wait for a participant to join
      logger.info('Waiting for participant to join...');
      const participant = await ctx.waitForParticipant();
      logger.info(`Starting AI agent for participant: ${participant.identity}`);

      // Configure the AI model based on environment
      let model: openai.realtime.RealtimeModel;

      if (config.azure.endpoint) {
        logger.info('Using Azure OpenAI configuration');
        model = openai.realtime.RealtimeModel.withAzure({
          baseURL: config.azure.endpoint,
          azureDeployment: config.azure.deployment,
          apiKey: config.azure.apiKey,
          entraToken: config.azure.entraToken,
          instructions: config.agent.systemPrompt,
          voice: config.agent.voice as any,
          temperature: config.agent.temperature,
        });
      } else {
        logger.info('Using OpenAI configuration');
        model = new openai.realtime.RealtimeModel({
          apiKey: config.openai.apiKey,
          instructions: config.agent.systemPrompt,
          voice: config.agent.voice as any,
          temperature: config.agent.temperature,
          model: config.openai.model,
        });
      }

      // Create function context for the agent
      const fncCtx = createFunctionContext();

      // Initialize the multimodal agent

      const agent = new multimodal.MultimodalAgent({
        model,
        fncCtx,
      });

      logger.info('Starting multimodal agent session...');

      // Start the agent session
      const session = await agent
        .start(ctx.room, participant)
        .then((session) => session as openai.realtime.RealtimeSession);

      session.on('input_speech_transcription_completed', async (event: any) => {
        const userInput = event.transcript;
        if (userInput && userInput.trim()) {
          logger.info('User input received', { userInput });
          // Process user input here
        }
      });

      session.on("conversation_item_created", (event: any) => {
        logger.info('Conversation item created', { event });
      });

      session.on("conversation_item_input_audio_transcription_completeted", (event: any) => {
        logger.info('Conversation item input audio transcription completed', { event });
      });

      // Set up session event handlers
      session.on('response_created', (response) => {
        logger.debug('AI response created', { responseId: response.id });
      });

      session.on('response_done', (response) => {
        logger.debug('AI response completed', { responseId: response.id });
      });

      session.on('error', (error) => {
        logger.error('Session error occurred', { error: error.message });
      });

      // Send initial greeting
      logger.info('Sending initial greeting to participant');
      session.conversation.item.create(
        llm.ChatMessage.create({
          role: llm.ChatRole.USER,
          text: config.agent.initialGreeting,
        })
      );

      // Generate the initial response
      session.response.create();

      logger.info('Agent session started successfully');

    } catch (error) {
      logger.error('Error in agent entry point', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  },
});

// CLI runner
if (import.meta.url === `file://${process.argv[1]}`) {
  cli.runApp(new WorkerOptions({
    agent: fileURLToPath(import.meta.url),
    logLevel: config.agent.logLevel as any,
  }));
}
