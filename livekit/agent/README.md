# LiveKit AI Agent

A production-ready AI agent implementation for real-time conversations using LiveKit's agents-js framework. This agent supports 1:1 patient-bot conversations with speech-to-text, AI responses, and text-to-speech capabilities.

## Features

- **Real-time Audio Processing**: Speech-to-text and text-to-speech
- **AI-Powered Conversations**: Uses OpenAI's GPT models for intelligent responses
- **1:1 Patient Conversations**: Designed for healthcare/consultation scenarios
- **Production Ready**: Comprehensive error handling, logging, and configuration
- **Flexible Configuration**: Support for OpenAI and Azure OpenAI
- **Function Calling**: Built-in functions for time queries and conversation management

## Prerequisites

- Node.js 18+ or Bun runtime
- LiveKit server instance
- OpenAI API key or Azure OpenAI setup

## Installation

1. Install dependencies:
```bash
bun install
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Configure your environment variables in `.env`:
```bash
# LiveKit Configuration (Required)
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# OpenAI Configuration (Required)
OPENAI_API_KEY=your-openai-api-key
```

## Usage

### Development Mode
```bash
bun run dev
```

### Production Mode
```bash
bun run start
```

### Connect to Specific Room
```bash
bun run connect --room patient-room-123
```

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LIVEKIT_URL` | LiveKit server WebSocket URL | Required |
| `LIVEKIT_API_KEY` | LiveKit API key | Required |
| `LIVEKIT_API_SECRET` | LiveKit API secret | Required |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `OPENAI_MODEL` | OpenAI model to use | `gpt-4o-realtime-preview-2024-10-01` |
| `AGENT_VOICE` | Voice for TTS | `alloy` |
| `AGENT_TEMPERATURE` | AI response creativity | `0.7` |
| `AGENT_LOG_LEVEL` | Logging level | `info` |

### Available Voices
- `alloy` - Neutral, balanced voice
- `echo` - Clear, professional voice
- `fable` - Warm, conversational voice
- `onyx` - Deep, authoritative voice
- `nova` - Bright, energetic voice
- `shimmer` - Soft, gentle voice

## Integration with Your Server

The agent works with the enhanced server that provides these endpoints:

### Create Patient Room
```bash
POST /createPatientRoom
Content-Type: application/json

{
  "patientId": "patient-123",
  "patientName": "John Doe"
}
```

Response:
```json
{
  "roomName": "patient-room-patient-123",
  "patientToken": "jwt-token-for-patient",
  "agentToken": "jwt-token-for-agent",
  "patientIdentity": "John Doe"
}
```

## Architecture

```
Patient Client ←→ LiveKit Server ←→ AI Agent
                      ↓
                 Room Management
                 Token Generation
                 Audio Processing
```

## Error Handling

The agent includes comprehensive error handling:
- Connection failures with automatic retry
- Audio processing errors
- AI model failures with graceful degradation
- Logging for debugging and monitoring

## Logging

Structured logging with configurable levels:
- `debug` - Detailed debugging information
- `info` - General operational messages
- `warn` - Warning conditions
- `error` - Error conditions

## Security Considerations

- Tokens have appropriate TTL (Time To Live)
- Agent permissions are restricted to necessary capabilities
- Environment variables for sensitive configuration
- Room isolation for patient privacy

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check LIVEKIT_URL and credentials
2. **No Audio**: Verify microphone permissions in browser
3. **AI Not Responding**: Check OpenAI API key and quota
4. **Room Not Found**: Ensure room is created via server endpoint

### Debug Mode

Run with debug logging:
```bash
AGENT_LOG_LEVEL=debug bun run dev
```

## Development

### Project Structure
```
src/
├── agent.ts      # Main agent implementation
├── config.ts     # Configuration management
└── logger.ts     # Logging utilities
```

### Building
```bash
bun run build
```

### Type Checking
```bash
bun run type-check
```

## License

This project follows the same license as your main project.
