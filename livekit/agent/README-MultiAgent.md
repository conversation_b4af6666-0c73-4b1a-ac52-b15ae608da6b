# Multi-Agent System for LiveKit

A sophisticated multi-agent system built on top of LiveKit agents-js that implements intelligent conversation routing with specialized agents for different tasks.

## 🏗️ Architecture Overview

The multi-agent system follows a **Router → Specialized Agent** pattern inspired by modern AI frameworks:

```
User Input → Voice Agent → Router Agent → Specialized Agent → Response
                              ↓
                    ┌─────────────────────┐
                    │   Router Agent      │
                    │ (Intent Detection)  │
                    └─────────────────────┘
                              ↓
        ┌─────────────────────────────────────────────────────┐
        │                                                     │
        ▼                    ▼                    ▼           ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Booking   │    │   Cancel    │    │     FAQ     │    │   Future    │
│   Agent     │    │   Agent     │    │   Agent     │    │   Agents    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🤖 Agent Types

### 1. Router Agent
- **Purpose**: Detects user intent and routes to appropriate specialized agent
- **Capabilities**: Intent classification (BOOK, CANCEL, QUESTION, GREETING, UNKNOWN)
- **Technology**: Keyword-based detection with future LLM enhancement

### 2. Booking Agent
- **Purpose**: Handles appointment scheduling and booking
- **Capabilities**: 
  - Date/time validation
  - Appointment creation
  - Availability checking
  - Confirmation handling
- **Tools**: `createAppointment` function

### 3. Cancel Agent
- **Purpose**: Manages appointment cancellations and rescheduling
- **Capabilities**:
  - Cancellation processing
  - Reason collection
  - Rescheduling options
- **Tools**: `cancelAppointment` function

### 4. FAQ Agent
- **Purpose**: Answers general questions about the medical practice
- **Capabilities**:
  - Knowledge base queries
  - Information retrieval
  - Hours, pricing, location info
- **Knowledge Base**: Configurable practice information

## 🚀 Getting Started

### Prerequisites
- LiveKit server running
- OpenAI API key configured
- Node.js/Bun runtime

### Installation
```bash
cd livekit/agent
bun install
```

### Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit with your credentials
LIVEKIT_URL=ws://localhost:7880
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-secret
OPENAI_API_KEY=your-openai-key

# Multi-agent specific settings
MULTI_AGENT_ROUTER_ENABLED=true
MULTI_AGENT_BOOKING_ENABLED=true
MULTI_AGENT_CANCEL_ENABLED=true
MULTI_AGENT_FAQ_ENABLED=true
```

### Running the System

#### Test Configuration
```bash
bun run test:multi
```

#### Development Mode
```bash
bun run dev:multi
```

#### Production Mode
```bash
bun run start:multi
```

## 💬 Conversation Flows

### Booking Flow
```
User: "Bonjour, je voudrais prendre rendez-vous"
Router: Detects BOOK intent → Handoff to Booking Agent
Booking: "Je vais vous aider à prendre rendez-vous. Quelle date vous conviendrait ?"
User: "Demain matin si possible"
Booking: "Parfait ! Je vous confirme votre rendez-vous pour le [date]. Vous recevrez une confirmation."
```

### Cancellation Flow
```
User: "Je dois annuler mon rendez-vous"
Router: Detects CANCEL intent → Handoff to Cancel Agent
Cancel: "Je comprends que vous souhaitez annuler un rendez-vous. Pouvez-vous me confirmer ?"
User: "Oui, je confirme"
Cancel: "Votre rendez-vous a été annulé avec succès. Souhaitez-vous en reprendre un autre ?"
```

### FAQ Flow
```
User: "Quels sont vos horaires ?"
Router: Detects QUESTION intent → Handoff to FAQ Agent
FAQ: "Nos horaires d'ouverture sont du lundi au vendredi de 8h à 18h, et le samedi de 9h à 12h."
```

## ⚙️ Configuration Options

### Agent Configuration
```typescript
// Enable/disable specific agents
MULTI_AGENT_ROUTER_ENABLED=true
MULTI_AGENT_BOOKING_ENABLED=true
MULTI_AGENT_CANCEL_ENABLED=true
MULTI_AGENT_FAQ_ENABLED=true

// Session settings
MULTI_AGENT_MAX_CONVERSATION_LENGTH=50
MULTI_AGENT_INACTIVITY_TIMEOUT=10

// Voice settings
MULTI_AGENT_VOICE_LANGUAGE=fr-FR
```

### Knowledge Base Configuration
```bash
# Practice information for FAQ agent
CABINET_HOURS="Lundi-Vendredi: 8h-18h, Samedi: 9h-12h"
CABINET_ADDRESS="123 Rue de la Santé, 75000 Paris"
CABINET_PHONE="01 23 45 67 89"
CABINET_EMAIL="<EMAIL>"
```

## 🔧 Advanced Features

### Session Memory
Each conversation maintains:
- **Intent History**: Track of detected intents
- **Conversation History**: Full message log with agent attribution
- **User Profile**: Patient information and preferences
- **Appointment State**: Current booking/cancellation status

### Agent Handoffs
- **Dynamic Routing**: Router agent determines best specialized agent
- **Context Preservation**: Conversation history maintained across handoffs
- **Fallback Handling**: Unknown intents handled gracefully
- **Multi-turn Conversations**: Agents can handle complex, multi-step interactions

### Error Handling
- **Intent Detection Failures**: Fallback to clarification requests
- **Agent Unavailability**: Graceful degradation
- **API Failures**: Retry mechanisms and user-friendly error messages
- **Session Timeouts**: Automatic cleanup and user notification

## 🧪 Testing

### Run All Tests
```bash
bun run test:multi
```

### Test Results Include:
- ✅ Configuration validation
- ✅ Intent detection accuracy
- ✅ Conversation flow testing
- ✅ Knowledge base completeness
- ✅ Agent capability verification

### Example Test Output:
```
🎯 Intent Detection Results: 5 passed, 1 failed
📚 Knowledge Base: All fields configured
🤖 Agent Capabilities: All agents enabled and functional
```

## 🔄 Integration with Client

### Multi-Agent Client Features
- **Real-time Agent Status**: Shows current active agent
- **Intent Display**: Visual feedback on detected intentions
- **Conversation History**: Track of agent handoffs
- **Enhanced UI**: Specialized interface for multi-agent interactions

### Usage in Client
```typescript
// Select Multi-Agent mode in the client
// The system automatically:
// 1. Connects to LiveKit room
// 2. Initializes voice capabilities
// 3. Starts with Router agent
// 4. Handles agent handoffs transparently
```

## 🚀 Deployment

### Development
```bash
# Terminal 1: Start LiveKit server
./livekit-server --dev

# Terminal 2: Start enhanced server
cd livekit/server && bun run server.ts

# Terminal 3: Start multi-agent system
cd livekit/agent && bun run dev:multi

# Terminal 4: Start client
cd livekit/client && bun run dev
```

### Production Considerations
- **Scaling**: Each agent can be scaled independently
- **Monitoring**: Comprehensive logging for agent interactions
- **Performance**: Optimized for real-time conversation flows
- **Security**: Token-based authentication and room isolation

## 🔮 Future Enhancements

### Planned Features
- **LLM-based Intent Detection**: Replace keyword matching with GPT-4
- **Advanced Agent Types**: Medical specialist agents, triage agents
- **Multi-language Support**: Automatic language detection and switching
- **Analytics Dashboard**: Conversation analytics and agent performance metrics
- **Integration APIs**: Connect with external booking systems and EMRs

### Extensibility
The system is designed for easy extension:
- **New Agent Types**: Implement `BaseMultiAgent` class
- **Custom Tools**: Add function calling capabilities
- **Enhanced Routing**: Implement sophisticated intent detection
- **External Integrations**: Connect with third-party services

## 📞 Support

For issues and questions:
- Run `bun run test:multi` to validate configuration
- Check agent logs for debugging information
- Review conversation flows in the test scenarios
- Consult the main LiveKit documentation for infrastructure issues

## 🤝 Contributing

To add new agents or enhance existing ones:
1. Extend the `BaseMultiAgent` class
2. Implement required abstract methods
3. Add agent configuration options
4. Update test scenarios
5. Document new conversation flows
