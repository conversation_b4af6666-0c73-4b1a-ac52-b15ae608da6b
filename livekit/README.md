# LiveKit AI Agent Integration

A complete implementation of LiveKit with AI agents for real-time conversations. This project includes a LiveKit server, React client, and AI agent that can participate in 1:1 patient consultations.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Client  │    │  Express Server │    │   AI Agent      │
│                 │    │                 │    │                 │
│ - Patient UI    │◄──►│ - Token Gen     │◄──►│ - Speech-to-Text│
│ - Video/Audio   │    │ - Room Mgmt     │    │ - AI Responses  │
│ - Controls      │    │ - API Endpoints │    │ - Text-to-Speech│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ LiveKit Server  │
                    │                 │
                    │ - WebRTC        │
                    │ - Room Mgmt     │
                    │ - Media Relay   │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

1. **LiveKit Server**: You need a running LiveKit server
   - [Download LiveKit Server](https://github.com/livekit/livekit/releases)
   - Or use [LiveKit Cloud](https://livekit.io/cloud)

2. **OpenAI API Key**: For AI agent functionality
   - Get your API key from [OpenAI](https://platform.openai.com/api-keys)

3. **Node.js/Bun**: Runtime environment
   - [Install Bun](https://bun.sh) (recommended) or Node.js 18+

### Environment Setup

1. **Create environment file for the agent:**
```bash
cd agent
cp .env.example .env
```

2. **Configure your environment variables:**
```bash
# LiveKit Configuration
LIVEKIT_URL=ws://localhost:7880  # Your LiveKit server URL
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
```

### Installation & Running

1. **Install dependencies for all components:**
```bash
# Server
cd server && bun install

# Client  
cd ../client && bun install

# Agent
cd ../agent && bun install
```

2. **Start LiveKit Server** (if running locally):
```bash
# Download and run LiveKit server
./livekit-server --dev
```

3. **Start the Express Server:**
```bash
cd server
bun run server.ts
```

4. **Start the AI Agent:**
```bash
cd agent
bun run dev
```

5. **Start the React Client:**
```bash
cd client
bun run dev
```

## 🎯 Usage

### Patient Consultation Mode

1. Open the React client (usually http://localhost:5173)
2. Select "Patient Consultation with AI Agent"
3. Enter a Patient ID (e.g., "patient-123")
4. Optionally enter a patient name
5. Click "Start Consultation"
6. The AI agent will automatically join and greet you
7. Start speaking - the agent will respond in real-time

### Traditional Video Conference

1. Select "Original Video Conference"
2. Join the default room for multi-participant video calls

## 📁 Project Structure

```
livekit/
├── server/          # Express.js server for token generation and room management
│   ├── server.ts    # Main server file with API endpoints
│   ├── package.json # Server dependencies
│   └── README.md    # Server documentation
├── client/          # React client application
│   ├── src/
│   │   ├── App.tsx        # Main app with mode selection
│   │   ├── PatientApp.tsx # Patient consultation interface
│   │   └── ...
│   ├── package.json # Client dependencies
│   └── README.md    # Client documentation
├── agent/           # AI agent implementation
│   ├── src/
│   │   ├── agent.ts       # Main agent implementation
│   │   ├── config.ts      # Configuration management
│   │   ├── logger.ts      # Logging utilities
│   │   └── test-agent.ts  # Test script
│   ├── package.json # Agent dependencies
│   ├── .env.example # Environment template
│   └── README.md    # Agent documentation
└── README.md        # This file
```

## 🔧 API Endpoints

The Express server provides these endpoints:

- `GET /getToken` - Get token for default room (backward compatibility)
- `POST /createPatientRoom` - Create patient room with AI agent
- `GET /room/:roomName` - Get room information
- `GET /rooms` - List all active rooms

### Creating a Patient Room

```bash
curl -X POST http://localhost:3000/createPatientRoom \
  -H "Content-Type: application/json" \
  -d '{"patientId": "patient-123", "patientName": "John Doe"}'
```

Response:
```json
{
  "roomName": "patient-room-patient-123",
  "patientToken": "jwt-token-for-patient",
  "agentToken": "jwt-token-for-agent", 
  "patientIdentity": "John Doe"
}
```

## 🤖 AI Agent Features

- **Real-time Speech Processing**: Converts speech to text using OpenAI Whisper
- **Intelligent Responses**: Uses GPT-4 for contextual conversations
- **Natural Voice**: Text-to-speech with multiple voice options
- **Function Calling**: Built-in functions for time queries and conversation management
- **Error Handling**: Comprehensive error handling and logging
- **Configurable**: Extensive configuration options for behavior tuning

## 🔒 Security & Privacy

- **Token-based Authentication**: Secure JWT tokens for room access
- **Room Isolation**: Each patient gets a private room
- **Limited Permissions**: Agents have minimal required permissions
- **Environment Variables**: Sensitive configuration via environment variables
- **TTL Tokens**: Tokens expire automatically for security

## 🛠️ Development

### Testing the Agent

```bash
cd agent
bun run test
```

This will validate your configuration and test connections.

### Debugging

Enable debug logging:
```bash
AGENT_LOG_LEVEL=debug bun run dev
```

### Building for Production

```bash
# Build all components
cd server && bun run build
cd ../client && bun run build  
cd ../agent && bun run build
```

## 📝 Configuration Options

### Agent Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `AGENT_VOICE` | TTS voice (alloy, echo, fable, onyx, nova, shimmer) | `alloy` |
| `AGENT_TEMPERATURE` | AI creativity (0-2) | `0.7` |
| `AGENT_SYSTEM_PROMPT` | Custom system prompt | Default healthcare prompt |
| `AGENT_LOG_LEVEL` | Logging level | `info` |

### Audio Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `AUDIO_SAMPLE_RATE` | Audio sample rate | `24000` |
| `AUDIO_CHANNELS` | Audio channels | `1` |

## 🚨 Troubleshooting

### Common Issues

1. **Agent won't connect**: Check LiveKit URL and credentials
2. **No audio**: Verify microphone permissions in browser
3. **AI not responding**: Check OpenAI API key and quota
4. **Room creation fails**: Ensure server is running and accessible

### Debug Steps

1. Test agent configuration: `cd agent && bun run test`
2. Check server logs for errors
3. Verify LiveKit server is running
4. Test API endpoints with curl
5. Check browser console for client errors

## 📄 License

This project follows the same license as your main project.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review component-specific README files
- Check LiveKit documentation
- Open an issue in the repository
