{"compilerOptions": {"checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2022"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "outDir": "./dist", "resolveJsonModule": true, "rootDir": ".", "skipLibCheck": true, "strict": true, "target": "es6"}, "exclude": ["node_modules", "dist"], "include": ["agent/**/*", "app.ts", "completion-server/**/*", "integration-server/**/*", "lib/**/*", "modules/**/*", "scripts/**/*.ts", "shared/**/*"]}