### Required Env Variables ###

# Your ngrok or server hostname, e.g. 123.ngrok.app
# nGrok provides free static domains: https://ngrok.com/blog-post/free-static-domains-ngrok-users
HOSTNAME=

TWILIO_ACCOUNT_SID=
# The Twilio auth token is only required to run setup script and it's only used to generate TWILIO_API_KEY & TWILIO_API_SECRET. If you provide the key/secret, then the auth token is is not required.
TWILIO_AUTH_TOKEN=

OPENAI_API_KEY=

### Auto-Provisioned Twilio Services ###
# These variables can be auto-generated by running the setup script or set here
TWILIO_API_KEY=
TWILIO_API_SECRET=

TWILIO_SYNC_SVC_SID=

# Used for placing calls and sending SMS when no "from" phone number is explicitly defined.
DEFAULT_TWILIO_NUMBER=

### Smart Transcripts & Agentic Recall ###
TWILIO_VOICE_INTELLIGENCE_SVC_SID=

### Flex ###
FLEX_WORKFLOW_SID=
FLEX_WORKSPACE_SID=
FLEX_QUEUE_SID=
FLEX_WORKER_SID=
# The Flex Conversations Service
TWILIO_CONVERSATIONS_SVC_SID=

### Personalization ###
# This is your (you the developer) personal phone number. It must be E164 format, i.e. +***********
DEVELOPERS_PHONE_NUMBER=
DEVELOPERS_EMAIL=
DEVELOPERS_FIRST_NAME=
DEVELOPERS_LAST_NAME=
