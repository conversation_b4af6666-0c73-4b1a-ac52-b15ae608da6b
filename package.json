{"author": "https://github.com/pBread", "name": "twilio-agentic-voice-bot-ts", "type": "module", "version": "1.0.0", "scripts": {"clear:sessions": "tsx scripts/sessions/clear.ts", "clear": "yarn clear:sessions", "dev": "nodemon --exec tsx app.ts --delay 1500ms --ignore /ui", "grok": "tsx scripts/ngrok.ts", "setup:apikey": "tsx scripts/setup/api-key.ts", "setup:flex": "tsx scripts/setup/flex.ts", "setup:info": "tsx scripts/setup/info.ts", "setup:phone": "tsx scripts/setup/phone.ts", "setup:sync": "tsx scripts/setup/sync.ts", "setup:ui": "tsx scripts/setup/ui.ts", "setup:vi": "tsx scripts/setup/voice-intelligence.ts", "setup": "tsx scripts/setup/index.ts", "ui": "cd ui && npm run dev"}, "dependencies": {"@twilio/conversations": "^2.6.1", "deep-diff": "^1.0.2", "dotenv-flow": "^4.1.0", "express": "^4.21.2", "express-ws": "^5.0.2", "lodash.debounce": "^4.0.8", "lodash.result": "^4.5.2", "openai": "^4.83.0", "p-queue": "^8.1.0", "twilio": "^5.4.3", "twilio-sync": "^3.3.5", "uuid": "^11.0.5", "ws": "^8.18.0"}, "devDependencies": {"@types/deep-diff": "^1.0.5", "@types/express": "^5.0.0", "@types/express-ws": "^3.0.5", "@types/lodash.debounce": "^4.0.9", "@types/lodash.result": "^4.5.9", "@types/node": "^22.13.1", "@types/p-queue": "^3.2.1", "@types/twilio": "^3.19.3", "@types/ws": "^8.5.14", "nodemon": "^3.1.9", "tsx": "^3.12.10", "typescript": "^5.7.3"}, "overrides": {"esbuild": "^0.25.0"}}