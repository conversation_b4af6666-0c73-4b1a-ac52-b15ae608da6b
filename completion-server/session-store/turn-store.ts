import { v4 as uuidv4 } from "uuid";
import { getMakeLogger } from "../../lib/logger.js";
import type {
  BotDTMFTurn,
  BotDTMFTurnParams,
  BotTextTurn,
  BotTextTurnParams,
  BotToolTurn,
  BotToolTurnParams,
  HumanDTMFTurn,
  HumanDTMFTurnParams,
  HumanTextTurn,
  HumanTextTurnParams,
  SystemTurn,
  SystemTurnParams,
  TurnRecord,
} from "../../shared/session/turns.js";
import type { StoreEventEmitter } from "./index.js";
import { createVersionedObject } from "./versioning.js";

/**
 * Manages the storage and ordering of conversation turns between a bot and human
 */
export class TurnStore {
  private turnMap: Map<string, TurnRecord> = new Map(); // map order enforces turn ordering, not the order property on the turns
  private log: ReturnType<typeof getMakeLogger>;

  constructor(
    private callSid: string,
    private eventEmitter: StoreEventEmitter,
  ) {
    this.log = getMakeLogger(callSid);
  }

  /****************************************************
   Turn Sequential Ordering
  ****************************************************/
  private _currentOrder: number = 0; // order is a non-sequential incrementor. Each turn is only gauranteed to have an order value greater than the previous. In other words, order is not always exactly +1 greater than the previous.
  // currentOrder cannot be mutated by external methods to protect order sequence
  public get currentOrder() {
    return this._currentOrder;
  }
  private nextOrder = () => this._currentOrder++;

  /****************************************************
   Primitive Methods
  ****************************************************/
  delete = (id: string) => {
    const turn = this.get(id);
    const result = this.turnMap.delete(id);
    this.eventEmitter.emit("turnDeleted", id, turn);
    return result;
  };
  get = (id: string) => this.turnMap.get(id);
  list = () => [...this.turnMap.values()];

  /****************************************************
   Turn Record Creators
  ****************************************************/
  /**
   * Records a DTMF (touch-tone) utterance generated by the bot during the LLM
   * completion loop. This represents tones are transmitted from the bot to the
   * user's phone. Typically these are used when the bot is navigating an
   * interactive voice response (IVR) tree.
   */
  addBotDTMF = (params: BotDTMFTurnParams): BotDTMFTurn => {
    const id = params.id ?? uuidv4();

    const turn: BotDTMFTurn = createVersionedObject(
      {
        callSid: this.callSid,
        content: params.content,
        createdAt: new Date().toISOString(),
        id,
        order: this.nextOrder(),
        origin: params.origin,
        role: "bot",
        status: params.status,
        type: "dtmf",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /**
   * Records text generated by the bot during the LLM completion loop.
   * This represents the raw text output from the LLM before it gets processed by
   * text-to-speech services for actual speech synthesis.
   */

  addBotText = (params: BotTextTurnParams): BotTextTurn => {
    const id = params.id ?? uuidv4();

    const turn: BotTextTurn = createVersionedObject(
      {
        callSid: this.callSid,
        content: params.content,
        createdAt: new Date().toISOString(),
        id,
        order: this.nextOrder(),
        origin: params.origin,
        role: "bot",
        status: params.status,
        type: "text",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /**
   * Records a tool interaction during the LLM completion loop.
   * Each turn captures both the LLM's request to execute a tool and the reasult of
   * that execution. The turn is initially created with just the tool request, and
   * later updated with the result via setToolResult().
   */
  addBotTool = (params: BotToolTurnParams): BotToolTurn => {
    const id = params.id ?? uuidv4();

    const turn: BotToolTurn = createVersionedObject(
      {
        callSid: this.callSid,
        createdAt: new Date().toISOString(),
        id,
        order: this.nextOrder(),
        origin: params.origin,
        role: "bot",
        status: params.status,
        tool_calls: params.tool_calls,
        type: "tool",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /**
   * Adds a turn to the store representing DTMF tones entered by the user
   */
  addHumanDTMF = (params: HumanDTMFTurnParams): HumanDTMFTurn => {
    const id = params.id ?? uuidv4();

    const turn: HumanDTMFTurn = createVersionedObject(
      {
        callSid: this.callSid,
        content: params.content,
        createdAt: new Date().toISOString(),
        id,
        order: this.nextOrder(),
        origin: params.origin,
        role: "human",
        type: "dtmf",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /**
   * Adds a turn to the store representing the user's transcribed speech
   */
  addHumanText = (params: HumanTextTurnParams): HumanTextTurn => {
    const id = params.id ?? uuidv4();

    const turn: HumanTextTurn = createVersionedObject(
      {
        callSid: this.callSid,
        content: params.content,
        createdAt: new Date().toISOString(),
        id,
        order: this.nextOrder(),
        origin: params.origin,
        role: "human",
        type: "text",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /**
   * Adds a system turn to the store
   */
  addSystem = (params: SystemTurnParams): SystemTurn => {
    const id = params.id ?? uuidv4();

    const turn: SystemTurn = createVersionedObject(
      {
        callSid: this.callSid,
        content: params.content,
        createdAt: new Date().toISOString(),
        id,
        origin: params.origin,
        order: this.nextOrder(),
        role: "system",
        version: 0,
      },
      () => this.eventEmitter.emit("turnUpdated", id),
    );

    this.turnMap.set(turn.id, turn);
    this.eventEmitter.emit("turnAdded", turn);
    return turn;
  };

  /****************************************************
   Interruptions
  ****************************************************/
  /**
   * Handles the redaction of turns when an interruption occurs
   * @param interruptedClause - The text that was interrupted
   */
  redactInterruption = (interruptedClause: string) => {
    const updatedClause = this.handleTextRedaction(interruptedClause); // finds the interrupted turn, updates the clause to reflect what was spoken, deletes any subsequent bot turns
    this.handleToolInterruption(); // find any tools that were not complete, delete them and any filler phrases

    this.log.info(
      "store",
      `local state updated to reflect interruption: ${updatedClause}`,
    );
  };

  /**
   * Updates the content of interrupted text turns and removes unspoken turns
   * @param interruptedClause - The text that was interrupted
   */
  private handleTextRedaction = (interruptedClause: string) => {
    // LLMs generate text responses much faster than the words are spoken to the user. When an interruption occurs, there are turns stored in local state that were not and never will be communicated. These records need to be cleaned up or else the bot will think it said things it did not and the conversation will discombobulate.

    const _interruptedClause = interruptedClause.trim();
    // Step 1: Find the local turn record that was interrupted. Convo Relay tells you what chunk of text, typically a sentence or clause, was interrupted. That clause is used to find the interrupted turn.
    const turnsDecending = this.list().reverse();
    let interruptedTurn = turnsDecending.find(
      (turn) =>
        turn.role === "bot" &&
        turn.type === "text" &&
        turn.content.includes(_interruptedClause),
    ) as BotTextTurn | undefined;

    if (!interruptedTurn) {
      interruptedTurn = turnsDecending.find(
        (turn) => turn.role === "bot" && turn.type === "text",
      ) as BotTextTurn;

      interruptedTurn.content = clipString(interruptedTurn.content, 0.75);
    }

    // delete unspoken dtmf & text turns
    turnsDecending
      .filter(
        (turn) =>
          turn.order > interruptedTurn.order && // only delete turns after the interrupted turn
          turn.role === "bot" && // only bot turns, not system, are deleted
          (turn.type === "dtmf" || turn.type === "text"),
      )
      .forEach((turn) => this.delete(turn.id));

    // Step 3: Update the interrupted turn to reflect what was actually spoken. Note, sometimes the interruptedClause is very long. The bot may have spoken some or most of it. So, the question is, should the interrupted clause be included or excluded. Here, it is being included but it's a judgement call.
    const prevContent = interruptedTurn.content as string;
    const indexOfInterruption = prevContent.lastIndexOf(_interruptedClause); // find where to split the expected content
    const contentSplitWithInterrupt = prevContent.substring(
      0,
      indexOfInterruption + _interruptedClause.length,
    ); // substring with interruption

    const contentSplitWithOutInterrupt = prevContent.substring(
      0,
      indexOfInterruption,
    ); // substring without interruption

    // interrupted clause was not sufficient to find the location
    if (indexOfInterruption === -1)
      interruptedTurn.content = clipString(prevContent);
    // the entire statement was interrupted
    else if (!contentSplitWithInterrupt?.length)
      interruptedTurn.content = clipString(_interruptedClause);
    // interruption string too short to be reliable
    else if (_interruptedClause.length <= 3)
      interruptedTurn.content = clipString(prevContent);
    // usually a zero length interruptedClause but adding a check just in case
    else if (prevContent === contentSplitWithInterrupt)
      interruptedTurn.content =
        contentSplitWithOutInterrupt + clipString(_interruptedClause);
    // the split was successful
    else interruptedTurn.content = contentSplitWithInterrupt;

    interruptedTurn.status = "interrupted";

    return interruptedTurn.content;
  };

  /**
   * Handles user interruptions of a tool execution
   */
  private handleToolInterruption = () => {
    const turnsDecending = this.list().reverse();

    const interruptedTurn = turnsDecending.find(
      (turn) =>
        turn.role === "bot" &&
        turn.type === "tool" &&
        turn.status === "streaming",
    ) as BotToolTurn | undefined;

    if (!interruptedTurn) return;

    interruptedTurn.status = "interrupted";

    for (const tool of interruptedTurn.tool_calls) {
      if (tool.result) continue;
      // todo: add "aborted to the ToolResponse type"
      this.setToolResult(tool.id, {
        status: "aborted",
        reason:
          "The user started speaking while this tool was resolving. In other words, the user interrupted this tool before it was completed.",
      });
    }

    // update incomplete bot tools
    turnsDecending
      .filter(
        (turn) =>
          turn.order > interruptedTurn.order && // only delete turns after the interrupted turn
          turn.role === "bot" && // only bot turns, not system, are deleted
          turn.origin === "filler",
      )
      .forEach((turn) => this.delete(turn.id));
  };

  /****************************************************
   Tool Results
  ****************************************************/
  /**
   * Sets the result for a specific tool call. Note: one BotToolTurn may have multiple
   * tools. This will be called once for each tool call.
   */
  setToolResult = (toolId: string, result: object) => {
    const toolTurn = this.list().find(
      (turn) =>
        turn.role === "bot" &&
        turn.type === "tool" &&
        (turn as BotToolTurn).tool_calls.some((tool) => tool.id === toolId),
    ) as BotToolTurn | undefined;

    if (!toolTurn) return;

    const tool = toolTurn.tool_calls.find((tool) => tool.id === toolId);
    if (!tool) return;

    tool.result = result;
    return toolTurn;
  };
}

/** splits a string by a percent without braking word */
function clipString(item: string, pct: number = 0.65) {
  if (item.length <= 4) return item;

  const middle = Math.floor(item.length * pct);

  // find the end of the word that contains the middle character
  let truncateAt = middle;
  while (truncateAt < item.length && item[truncateAt] !== " ") truncateAt++;

  // if we hit the end and it's one long word, use the full string
  if (truncateAt === item.length && item.indexOf(" ") === -1) return item;
  return item.substring(0, truncateAt) + "...";
}
