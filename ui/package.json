{"name": "twilio-conversation-relay-ts-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "lint": "next lint"}, "dependencies": {"@mantine/core": "^7.17.0", "@mantine/hooks": "^7.17.0", "@reduxjs/toolkit": "^2.5.1", "@tabler/icons-react": "^3.30.0", "lodash.startcase": "^4.4.0", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "twilio-sync": "^3.3.6"}, "devDependencies": {"@types/lodash.startcase": "^4.4.9", "@types/node": "^20.17.19", "@types/react": "^19", "@types/react-dom": "^19", "@types/redux-logger": "^3.0.13", "typescript": "^5"}}