{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./*"], "@shared/*": ["../shared/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "../shared/**/*.ts", "../shared/**/*.tsx"], "exclude": ["node_modules"]}